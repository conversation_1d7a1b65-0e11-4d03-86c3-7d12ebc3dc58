<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REITs分析框架</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment@1.0.0/dist/chartjs-adapter-moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #004AAD;
            --secondary: #34a853;
            --tertiary: #fbbc05;
            --quaternary: #ea4335;
        }
        body { font-family: 'Noto Sans SC', sans-serif; background-color: #f8fafc; }
        .chart-container { position: relative; width: 100%; height: 380px; }
        .brilliant-blues-bg { background-color: #004AAD; }
        .brilliant-blues-text { color: #004AAD; }
        .card { background-color: #FFFFFF; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); padding: 1.5rem; }
        .control-panel { background-color: #f1f5f9; padding: 0.75rem; border-radius: 0.5rem; margin-bottom: 1rem; }
        table { width: 100%; font-size: 0.875rem; }
        th, td { padding: 0.5rem 0.75rem; text-align: left; border-bottom: 1px solid #e5e7eb; vertical-align: top; }
        th { background-color: #f9fafb; font-weight: 500; white-space: nowrap; }
        .date-slider-container { padding-top: 1.5rem; }
        .slider-track { position: relative; width: 100%; height: 6px; background-color: #e5e7eb; border-radius: 3px; }
        .slider-range { position: absolute; height: 100%; background-color: #004AAD; border-radius: 3px; }
        .slider-handle { position: absolute; top: -5px; width: 16px; height: 16px; background-color: #fff; border: 2px solid #004AAD; border-radius: 50%; cursor: grab; }
        .slider-handle:active { cursor: grabbing; }
        .date-labels { display: flex; justify-content: space-between; font-size: 0.75rem; color: #6b7280; margin-top: 0.5rem; }
        .nav-link { display: block; padding: 0.75rem 1rem; border-radius: 0.5rem; cursor: pointer; transition: background-color 0.2s, color 0.2s; }
        .nav-link.active { background-color: #004AAD; color: white; }
        .nav-link:not(.active):hover { background-color: #eef2ff; }
        #global-selector-panel label { display: block; padding: 0.5rem; cursor: pointer; border-radius: 0.25rem; }
        #global-selector-panel label:hover { background-color: #f3f4f6; }

        /* Styles for comparison grid */
        .comparison-control-panel { display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap; }
        .comparison-control-panel button, .comparison-control-panel select { padding: 8px 15px; border-radius: 4px; border: 1px solid #ddd; background: white; cursor: pointer; }
        .comparison-control-panel button.active { background: var(--primary); color: white; }
        .comparison-container { display: grid; /* grid-template-columns is set dynamically */ gap: 1px; background: #ddd; border: 1px solid #ddd; overflow-x: auto; }
        .comparison-header-row { display: contents; }
        .comparison-header-cell, .comparison-data-cell, .comparison-category-header { padding: 10px; background: white; }
        .comparison-header-cell { position: sticky; top: 0; z-index: 2; font-weight: bold; text-align: center; }
        .comparison-header-cell[data-role="field-name"] { position: sticky; left: 0; z-index: 1; text-align: left;}
        .comparison-data-cell { min-height: 40px; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; font-size: 0.8rem; }
        .comparison-category-header { grid-column: 1 / -1; background: #f1f3f4; font-weight: bold; }
        .progress-bar { height: 6px; background: #e0e0e0; border-radius: 3px; overflow: hidden; width: 80%; margin-top: 4px; }
        .progress-fill { height: 100%; background: var(--secondary); }
        .badge { padding: 3px 8px; border-radius: 12px; font-size: 12px; background: #e0e0e0; }
        .badge.yes { background: #c8e6c9; color: #256029; }
        .badge.no { background: #ffcdd2; color: #c63737; }
        .badge.pending { background: #ffecb3; color: #7a5900; }
        .highlight { background-color: #fff8e1 !important; }
        .visualization { margin-top: 20px; border-top: 1px solid #ddd; padding-top: 20px; }

        /* Factor Backtest Styles */
        .factor-metric-card {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            transition: box-shadow 0.3s ease;
        }
        .factor-metric-card:hover {
             box-shadow: 0 2px 4px rgba(0,0,0,0.08);
        }
        .factor-weight {
            width: 70px;
            display: inline-block;
        }
        .weight-total {
            font-weight: bold;
            color: var(--primary);
            font-size: 1.1rem;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary);
        }
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .loading-indicator {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 9999;
        }
        .spinner-border {
            position: absolute;
            top: 50%;
            left: 50%;
        }

        /* 优化因子回测模块样式 */
        .nav-tabs-sm .nav-link {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .factor-metric-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .factor-metric-card:hover {
            background: #e9ecef;
        }

        .weight-total {
            font-weight: 600;
            font-size: 0.875rem;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1.2;
        }

        .metric-label {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        /* 资产运营模块样式 */
        .asset-checkbox {
            width: 1rem;
            height: 1rem;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .chart-container {
                height: 250px !important;
            }

            .metric-value {
                font-size: 1.25rem;
            }

            .grid {
                grid-template-columns: 1fr;
            }
        }

        /* Ensure Bootstrap and Tailwind compatibility */
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
        }
        .nav-tabs .nav-link.active {
            color: var(--primary);
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }

        /* Chart container responsive adjustments */
        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }
            .comparison-container {
                font-size: 0.75rem;
            }
            .metric-value {
                font-size: 1.5rem;
            }
        }

        /* Ensure proper spacing for different sections */
        .tab-content > .tab-pane {
            padding-top: 1rem;
        }

        /* Fix potential z-index issues */
        .nav-tabs {
            z-index: 1;
        }

        /* Ensure form controls work well together */
        .form-select, .form-control {
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        /* 导航链接样式 */
        .nav-link {
            display: block;
            padding: 0.5rem 0.75rem;
            color: #6b7280;
            text-decoration: none;
            border-radius: 0.375rem;
            transition: all 0.15s ease-in-out;
            cursor: pointer;
        }

        .nav-link:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .nav-link.active {
            background-color: #3b82f6;
            color: white;
        }
        .report-item {
            border-bottom: 1px solid #e5e7eb;
        }
        .report-summary {
            padding: 1rem;
            cursor: pointer;
            background-color: #f9fafb;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .report-summary:hover {
            background-color: #f3f4f6;
        }
        .report-full {
            display: none;
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
        }
    </style>
</head>
<body class="text-gray-800">

    <header class="brilliant-blues-bg text-white p-6 text-center">
        <h1 class="text-3xl md:text-4xl">REITs分析框架</h1>
    </header>

    <div class="container mx-auto p-4 md:p-6">
        <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
            
            <nav class="lg:col-span-1">
                <div class="sticky top-6 flex flex-col gap-4">
                    <div class="card p-4">
                        <h3 class="text-lg font-semibold border-b pb-2 mb-3">导航栏</h3>
                        <ul id="main-nav" class="space-y-1">
                            <li><a data-section="section-basic-info" class="nav-link active">基础信息</a></li>
                            <li><a data-section="section-asset-valuation" class="nav-link">资产估值</a></li>
                            <li><a data-section="section-asset-ops" class="nav-link">资产运营</a></li>
                            <li><a data-section="section-financials" class="nav-link">财务数据</a></li>
                            <li><a data-section="section-trading" class="nav-link">估值交易</a></li>
                            <li><a data-section="section-backtest" class="nav-link">投资决策</a></li>
                            <li><a data-section="section-reports" class="nav-link">投研报告</a></li>
                            <li><a data-section="section-risk" class="nav-link">风控预警</a></li>
                        </ul>
                    </div>
                    <div class="card p-4">
                        <h3 class="text-lg font-semibold border-b pb-2 mb-3">选择分析标的</h3>
                        <div id="global-selector-panel" class="space-y-1 max-h-60 overflow-y-auto"></div>
                    </div>
                </div>
            </nav>

            <main id="main-content" class="lg:col-span-4 flex flex-col gap-6">
                <section id="section-basic-info" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">基础信息对比</h2>
                    <div class="comparison-control-panel">
                        <button id="toggle-differences" class="active">高亮差异</button>
                        <select id="category-filter">
                            <option value="all">所有分类</option>
                            <!-- Options will be dynamically populated -->
                        </select>
                        <button id="export-csv">导出CSV</button>
                    </div>
                    <div class="comparison-container" id="comparison-grid">
                        <!-- Dynamically generated content -->
                    </div>
                </section>

                <section id="section-asset-valuation" class="card hidden">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">资产估值分析</h2>
                    <h3 class="text-xl font-semibold mb-3">历史估值变化</h3>
                    <div class="chart-container"><canvas id="asset-valuation-chart"></canvas></div>
                    <h3 class="text-xl font-semibold mt-8 mb-3">估值参数</h3>
                    <div class="overflow-x-auto">
                        <table id="asset-valuation-params-table"></table>
                    </div>
                </section>

                <section id="section-asset-ops" class="card hidden">
                    <div class="mb-6">
                        <h2 class="text-2xl text-center mb-2 brilliant-blues-text">资产运营分析</h2>
                        <p class="text-center text-gray-600 text-sm">基于左侧选择的REITs标的进行多资产运营数据分析</p>
                    </div>

                    <!-- 资产选择器 -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold text-gray-800">资产选择</h3>
                            <div class="text-sm text-gray-600">
                                已选择 <span id="selected-reits-count" class="font-semibold text-blue-600">0</span> 个REITs，
                                包含 <span id="total-assets-count" class="font-semibold text-green-600">0</span> 个资产
                            </div>
                        </div>
                        <div id="asset-selector-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- 资产选择器将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 可视化分析区域 -->
                    <div id="asset-analysis-content">
                        <!-- 初始提示 -->
                        <div id="asset-initial-message" class="text-center py-16 px-6 bg-gray-50 rounded-lg">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V7a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="mt-2 text-lg font-medium text-gray-900">请选择REITs和资产</h3>
                            <p class="mt-1 text-sm text-gray-500">从左侧选择REITs标的，然后在上方选择具体资产来查看运营数据分析。</p>
                        </div>

                        <!-- 分析图表区域 -->
                        <div id="asset-charts-container" class="hidden">
                            <!-- 关键指标卡片 -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                <div class="bg-white border rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-blue-600" id="avg-occupancy">--</div>
                                    <div class="text-sm text-gray-600">平均出租率</div>
                                </div>
                                <div class="bg-white border rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-green-600" id="avg-rent">--</div>
                                    <div class="text-sm text-gray-600">平均租金(元/㎡/月)</div>
                                </div>
                                <div class="bg-white border rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-purple-600" id="total-area">--</div>
                                    <div class="text-sm text-gray-600">总可租面积(万㎡)</div>
                                </div>
                                <div class="bg-white border rounded-lg p-4 text-center">
                                    <div class="text-2xl font-bold text-orange-600" id="avg-lease-term">--</div>
                                    <div class="text-sm text-gray-600">平均剩余租期(年)</div>
                                </div>
                            </div>

                            <!-- 图表网格 -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                                <div class="bg-white border rounded-lg p-4">
                                    <h4 class="text-lg font-semibold mb-3 text-center">出租率趋势对比</h4>
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="multi-occupancy-chart"></canvas>
                                    </div>
                                </div>
                                <div class="bg-white border rounded-lg p-4">
                                    <h4 class="text-lg font-semibold mb-3 text-center">租金趋势对比</h4>
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="multi-rent-chart"></canvas>
                                    </div>
                                </div>
                                <div class="bg-white border rounded-lg p-4 lg:col-span-2">
                                    <h4 class="text-lg font-semibold mb-3 text-center">行业分布分析</h4>
                                    <div id="multi-industry-container" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <!-- Charts will be generated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细数据表格 -->
                    <div id="asset-data-table-container" class="bg-white border rounded-lg p-4 mt-6 hidden">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">选中资产详细数据</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-600">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-4 py-3">REITs代码</th>
                                        <th scope="col" class="px-4 py-3">基金简称</th>
                                        <th scope="col" class="px-4 py-3">资产名称</th>
                                        <th scope="col" class="px-4 py-3">类型</th>
                                        <th scope="col" class="px-4 py-3">所在地</th>
                                        <th scope="col" class="px-4 py-3 text-right">可租赁面积(万㎡)</th>
                                        <th scope="col" class="px-4 py-3 text-right">当前出租率(%)</th>
                                        <th scope="col" class="px-4 py-3 text-right">当前租金(元/㎡/月)</th>
                                        <th scope="col" class="px-4 py-3 text-right">剩余租期(年)</th>
                                        <th scope="col" class="px-4 py-3 text-right">前五大租户占比(%)</th>
                                    </tr>
                                </thead>
                                <tbody id="asset-data-table-body">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <section id="section-financials" class="card hidden">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">财务数据分析</h2>
                    

                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <h3 class="text-xl font-semibold mb-3">EBITDA利润率趋势</h3>
                            <div class="chart-container">
                                <div id="ebitda-chart" style="height: 380px;"></div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold mb-3">可供分配金额趋势</h3>
                            <div class="chart-container">
                                <div id="distribution-chart" style="height: 380px;"></div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold mb-3">可供分配金额增长分析</h3>
                            <div class="chart-container">
                                <div id="growth-chart" style="height: 380px;"></div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-xl font-semibold mb-3">收入结构分析</h3>
                                <div class="chart-container">
                                    <div id="structure-chart" style="height: 380px;"></div>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-xl font-semibold mb-3">成本结构分析</h3>
                                <div class="chart-container">
                                    <div id="cost-chart" style="height: 380px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="section-trading" class="card hidden">
                     <h2 class="text-2xl text-center mb-4 brilliant-blues-text">估值与交易</h2>
                     <div class="flex flex-col gap-6">
                         <div>
                             <h3 class="text-xl text-center mb-4 font-semibold">近期表现</h3>
                             <div class="overflow-x-auto">
                                 <table id="performance-table"></table>
                             </div>
                         </div>
                         <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
                             <div>
                                 <h3 class="text-xl text-center mb-4 font-semibold">市场表现</h3>
                                 <div class="chart-container"><canvas id="price-turnover-chart"></canvas></div>
                                 <div id="price-slider-container"></div>
                             </div>
                             <div>
                                 <h3 class="text-xl text-center mb-4 font-semibold">多维估值</h3>
                                 <div class="control-panel">
                                     <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                         <div><label for="bubble-x-axis" class="text-sm font-medium">X轴:</label><select id="bubble-x-axis" class="p-1 border rounded w-full"></select></div>
                                         <div><label for="bubble-y-axis" class="text-sm font-medium">Y轴:</label><select id="bubble-y-axis" class="p-1 border rounded w-full"></select></div>
                                         <div><label for="bubble-size" class="text-sm font-medium">气泡大小:</label><select id="bubble-size" class="p-1 border rounded w-full"></select></div>
                                     </div>
                                 </div>
                                 <div class="chart-container"><canvas id="reits-bubble-chart"></canvas></div>
                             </div>
                         </div>
                         <div>
                             <h3 class="text-xl text-center mb-4 font-semibold">估值比较</h3>
                              <div class="control-panel">
                                   <div class="grid grid-cols-2 md:grid-cols-4 gap-4" id="radar-metrics-selectors"></div>
                             </div>
                             <div class="chart-container"><canvas id="radar-chart"></canvas></div>
                         </div>
                     </div>
                </section>

                <section id="section-reports" class="card hidden">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">投研报告</h2>
                     <div class="control-panel space-y-4">
                        <div class="relative">
                            <button id="report-metrics-button" class="w-full bg-white border border-gray-300 rounded p-2 text-left">选择指标</button>
                            <div id="report-metrics-dropdown" class="hidden absolute z-10 w-full bg-white border rounded mt-1 p-2 space-y-2">
                                <!-- Dynamic content here -->
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <input type="date" id="report-start-date" class="p-1 border rounded w-full">
                            <span>至</span>
                            <input type="date" id="report-end-date" class="p-1 border rounded w-full">
                        </div>
                        <button id="generate-report-btn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition w-full">生成研报</button>
                    </div>
                    <div id="report-output" class="mt-4 space-y-2">
                        <p class="text-gray-500 initial-message">选择标的、指标和时间区间后，点击按钮生成研报内容...</p>
                    </div>
                </section>
                <section id="section-risk" class="card hidden">
                     <h2 class="text-2xl text-center mb-4 brilliant-blues-text">风控预警</h2>
                     <div class="control-panel flex flex-wrap items-center justify-between gap-4">
                         <div>
                             <label class="font-semibold">预警指标:</label>
                             <div id="risk-metrics-selector" class="flex flex-wrap gap-x-4 gap-y-2 mt-2"></div>
                         </div>
                         <button id="risk-settings-btn" class="text-sm bg-gray-200 px-3 py-1 rounded hover:bg-gray-300">设置阈值</button>
                     </div>
                     <div class="chart-container mb-6" style="height: 300px;"><canvas id="risk-visualization-chart"></canvas></div>
                     <div class="overflow-x-auto">
                         <table id="risk-warning-table"></table>
                     </div>
                </section>
                <section id="section-backtest" class="card hidden">
                    <div class="loading-indicator" id="loadingIndicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h2 class="text-2xl text-center mb-2 brilliant-blues-text">REITs回测系统</h2>
                        <p class="text-center text-gray-600 text-sm">基于可选因子的REITs投资策略分析与回测平台</p>
                    </div>

                    <!-- 配置面板 -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <ul class="nav nav-tabs nav-tabs-sm mb-3" id="configTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active text-sm" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-config" type="button" role="tab">基本配置</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link text-sm" id="factor-tab" data-bs-toggle="tab" data-bs-target="#factor-config" type="button" role="tab">因子配置</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="configTabContent">
                            <div class="tab-pane fade show active" id="basic-config" role="tabpanel">
                                <div class="row g-3">
                                    <div class="col-md-6 col-lg-3">
                                        <label for="reit-type" class="form-label text-sm font-medium">REITs类型</label>
                                        <select class="form-select form-select-sm" id="reit-type" multiple size="3">
                                            <option value="retail" selected>园区基础设施</option>
                                            <option value="office" selected>交通基础设施</option>
                                            <option value="industrial" selected>仓储物流</option>
                                            <option value="residential" selected>能源基础设施</option>
                                            <option value="healthcare" selected>消费基础设施</option>
                                            <option value="hotel">生态环保</option>
                                            <option value="mixed">新型基础设施</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 col-lg-3">
                                        <label for="time-period" class="form-label text-sm font-medium">回测周期</label>
                                        <select class="form-select form-select-sm" id="time-period">
                                            <option value="1y">近1年</option>
                                            <option value="3y">近3年</option>
                                            <option value="5y" selected>近5年</option>
                                            <option value="10y">近10年</option>
                                            <option value="max">最大周期</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 col-lg-3">
                                        <label for="rebalance-freq" class="form-label text-sm font-medium">调仓频率</label>
                                        <select class="form-select form-select-sm" id="rebalance-freq">
                                            <option value="monthly">每月</option>
                                            <option value="quarterly" selected>每季度</option>
                                            <option value="semiannually">每半年</option>
                                            <option value="annually">每年</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 col-lg-3">
                                        <label for="holding-count" class="form-label text-sm font-medium">持仓数量</label>
                                        <input type="number" class="form-control form-control-sm" id="holding-count" value="10" min="1" max="50">
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="factor-config" role="tabpanel">
                                <div id="validation-alert" class="mb-3"></div>
                                <div class="row g-3" id="factor-container">
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
                                    <div class="d-flex align-items-center">
                                        <span class="text-sm text-gray-600 me-2">权重总计:</span>
                                        <span id="weight-total" class="weight-total badge bg-primary">100%</span>
                                    </div>
                                    <div>
                                        <button class="btn btn-primary btn-sm" id="run-backtest">
                                            <i class="bi bi-play-fill"></i> 开始回测
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm ms-2" id="export-results">
                                            <i class="bi bi-download"></i> 导出结果
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 结果展示区域 -->
                    <div class="bg-white rounded-lg border">
                        <ul class="nav nav-tabs nav-tabs-sm" id="resultTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active text-sm" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">绩效分析</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link text-sm" data-bs-toggle="tab" data-bs-target="#portfolio" type="button" role="tab">最新持仓</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link text-sm" data-bs-toggle="tab" data-bs-target="#factor-analysis" type="button" role="tab">因子分析</button>
                            </li>
                        </ul>

                        <div class="tab-content p-4" id="resultTabContent">
                            <div class="tab-pane fade show active" id="performance" role="tabpanel">
                                <!-- 关键指标卡片 -->
                                <div class="row g-3 mb-4">
                                    <div class="col-md-4">
                                        <div class="card text-center h-100">
                                            <div class="card-body py-3">
                                                <div class="metric-value text-primary" id="total-return">--</div>
                                                <div class="metric-label text-muted small">累计收益率</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card text-center h-100">
                                            <div class="card-body py-3">
                                                <div class="metric-value text-success" id="annualized-return">--</div>
                                                <div class="metric-label text-muted small">年化收益率</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card text-center h-100">
                                            <div class="card-body py-3">
                                                <div class="metric-value text-danger" id="max-drawdown">--</div>
                                                <div class="metric-label text-muted small">最大回撤</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 图表区域 -->
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="chart-container" style="height: 300px;">
                                            <canvas id="performance-chart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="chart-container" style="height: 250px;">
                                            <canvas id="monthly-return-chart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="portfolio" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-sm" id="portfolio-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th class="text-center">代码</th><th>名称</th><th class="text-center">类型</th><th class="text-center">权重</th>
                                                <th class="text-center">股息率</th><th class="text-center">FFO增长率</th><th class="text-center">NAV折溢价</th><th class="text-center">综合得分</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="factor-analysis" role="tabpanel">
                                <div class="row g-3 mb-4">
                                    <div class="col-md-6">
                                        <div class="chart-container" style="height: 300px;">
                                            <canvas id="factor-contribution-chart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" style="height: 300px;">
                                            <canvas id="factor-correlation-chart"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead class="table-light">
                                            <tr><th>因子</th><th class="text-center">IC均值</th><th class="text-center">IR比率</th><th class="text-center">胜率</th></tr>
                                        </thead>
                                        <tbody id="factor-metrics-table"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>
    
    <!-- Risk Settings Modal -->
    <div id="risk-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">设置预警阈值</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">预警线比例 (现价/买入均价)</label>
                    <input type="number" id="warning-line-ratio" value="0.8" step="0.01" class="mt-1 block w-full p-2 border border-gray-300 rounded-md">
                </div>
                 <div>
                    <label class="block text-sm font-medium text-gray-700">止损线比例 (现价/买入均价)</label>
                    <input type="number" id="stop-loss-line-ratio" value="0.7" step="0.01" class="mt-1 block w-full p-2 border border-gray-300 rounded-md">
                </div>
                 <div>
                    <label class="block text-sm font-medium text-gray-700">VaR 阈值</label>
                    <div class="grid grid-cols-2 gap-2 mt-1">
                        <input type="number" id="var-yellow-threshold" value="0.03" step="0.001" placeholder="黄色" class="block w-full p-2 border border-gray-300 rounded-md">
                        <input type="number" id="var-red-threshold" value="0.04" step="0.001" placeholder="红色" class="block w-full p-2 border border-gray-300 rounded-md">
                    </div>
                </div>
                 <div>
                    <label class="block text-sm font-medium text-gray-700">集中度 阈值 (%)</label>
                    <div class="grid grid-cols-2 gap-2 mt-1">
                        <input type="number" id="concentration-yellow-threshold" value="8" placeholder="黄色" class="block w-full p-2 border border-gray-300 rounded-md">
                        <input type="number" id="concentration-red-threshold" value="10" placeholder="红色" class="block w-full p-2 border border-gray-300 rounded-md">
                    </div>
                </div>
            </div>
            <div class="mt-6 flex justify-end gap-3">
                <button id="cancel-risk-settings" class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">取消</button>
                <button id="save-risk-settings" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">保存</button>
            </div>
        </div>
    </div>

    <!-- Data from the uploaded CSV file is stored here -->
    <script type="text/plain" id="csv-financial-data">
id,name,quarter,ebitda,distribution,rev_rent,rev_prop,rev_park,rev_other,cost_op,cost_dep,cost_tax,cost_mgmt
180101.SZ,博时蛇口产园REIT,2025-Q2,0.71,0.46,0.86,0.13,0,0.01,0.59,0.44,0.09,0.03
180102.SZ,华夏合肥高新REIT,2025-Q2,0.6,0.21,0.99,0,0,0.01,0.42,0.39,0.19,0.15
180103.SZ,华夏和达高科REIT,2025-Q2,0.59,0.22,0.82,0.12,0.02,0.04,0.56,0.46,0.15,0.17
180105.SZ,易方达广开产园REIT,2025-Q2,0.69,0.28,0.97,0,0.01,0.02,0.65,0.6,0.2,0.07
508000.SH,华安张江产业园REIT,2025-Q2,0.82,0.36,0.9,0.06,0.02,0.02,1,0.92,0.08,0.03
180101.SZ,博时蛇口产园REIT,2025-Q1,0.71,0.46,0.86,0.13,0,0.01,0.59,0.44,0.09,0.03
180102.SZ,华夏合肥高新REIT,2025-Q1,0.63,0.22,0.99,0,0,0.01,0.42,0.39,0.19,0.15
180103.SZ,华夏和达高科REIT,2025-Q1,0.61,0.23,0.82,0.12,0.02,0.04,0.56,0.46,0.15,0.17
180105.SZ,易方达广开产园REIT,2025-Q1,0.68,0.27,0.97,0,0.01,0.02,0.65,0.6,0.2,0.07
508000.SH,华安张江产业园REIT,2025-Q1,0.76,0.36,0.9,0.06,0.02,0.02,1,0.92,0.08,0.03
180101.SZ,博时蛇口产园REIT,2024-Q4,0.7,0.46,0.86,0.13,0,0.01,0.59,0.44,0.09,0.03
180102.SZ,华夏合肥高新REIT,2024-Q4,0.58,0.22,0.99,0,0,0.01,0.42,0.39,0.19,0.15
180103.SZ,华夏和达高科REIT,2024-Q4,0.47,0.27,0.82,0.12,0.02,0.04,0.56,0.46,0.15,0.17
180105.SZ,易方达广开产园REIT,2024-Q4,0.65,0.33,0.97,0,0.01,0.02,0.65,0.6,0.2,0.07
508000.SH,华安张江产业园REIT,2024-Q4,0.67,0.42,0.9,0.06,0.02,0.02,1,0.92,0.08,0.03
180101.SZ,博时蛇口产园REIT,2024-Q3,0.74,0.53,0.86,0.13,0,0.01,0.59,0.44,0.09,0.03
180102.SZ,华夏合肥高新REIT,2024-Q3,0.63,0.23,0.99,0,0,0.01,0.42,0.39,0.19,0.15
180103.SZ,华夏和达高科REIT,2024-Q3,0.58,0.24,0.82,0.12,0.02,0.04,0.56,0.46,0.15,0.17
508000.SH,华安张江产业园REIT,2024-Q3,0.71,0.34,0.9,0.06,0.02,0.02,1,0.92,0.08,0.03
180101.SZ,博时蛇口产园REIT,2024-Q2,0.7,0.53,0.86,0.13,0,0.01,0.59,0.44,0.09,0.03
180102.SZ,华夏合肥高新REIT,2024-Q2,0.59,0.23,0.99,0,0,0.01,0.42,0.39,0.19,0.15
180103.SZ,华夏和达高科REIT,2024-Q2,0.59,0.27,0.82,0.12,0.02,0.04,0.56,0.46,0.15,0.17
508000.SH,华安张江产业园REIT,2024-Q2,0.75,0.43,0.9,0.06,0.02,0.02,1,0.92,0.08,0.03
    </script>
    <script type="text/plain" id="csv-data">
代码,证券简称,REITs上市日期,资产类型,原始权益人,原始权益人性质,运营管理机构,原始权益人和运营管理机构关系,运营管理费（固定）,运营管理费（浮动）,管理人报酬,托管费,原始权益人增信措施, 原始权益人或其同一控制下关联方配售份额占总份额比例(首发),是否发布扩募公告,扩募是否获得批准
180101.SZ,博时招商蛇口产业园REIT,2021-06-21,园区基础设施,招商局光明科技园有限公司,中央国有企业,深圳市招商创业有限公司,子公司,基础管理费用=I×9.0%；I 表示项目公司当年经审计的营业收入，以项目公司 年度审计报告为准；,浮动管理费用=（C-T）×R，预算实现率≤90%，R=20%；90%<预算实现率<110%，R=10%；110%≤预算实现率，R=20%。特别地，当 C＜T 时，将相应扣减基础管理费用，扣减上限不超过外部管理机构当期确认的基础管理费用金额的 80%。,固定管理费用的 85%由基金管理人收取，15%由计划管理人收取。固定管理费用的 计算方法如下： H=E×0.22%÷当年天数 H 为当日应计提的固定管理费用； E 在基金成立首年为初始募集规模,0.0001,,0.32733403,2025-4-17，第二次扩募,第二次扩募尚未获得批准
180102.SZ,华夏合肥高新产园REIT,2022-10-10,园区基础设施,合肥高新城市发展集团有限公司,地方国有企业,合肥高新投资促进集团股份有限公司,,浮动管理费 A=运营收入*10.5%；,浮动管理费 B 按照实际运营净收益与目标运营净收益差额的 10%计提，浮动管理费 B 仅在实际运营净收益高于目标运营净收益时计提,0.0023,0.0002,,0.25,否,否
180103.SZ,华夏杭州和达高科产园REIT,2022-12-27,园区基础设施,杭州和达高科技发展集团有限公司,地方国有企业,杭州和达高科技发展集团有限公司,,基础服务报酬根管理费据项目公司运营收入的情况按季度计提，其中，孵化器项目为季度运营收入的16%;和达药谷一期项目2025年及之前为季度运营收入的16%，2026年及之后为季度运营收入的15%,"浮动服务报酬按年计提，浮动服务报酬= (运营收入净额实际值运营收入净额目标值) *15%*考核系数。",0.0024,0.0001,,0.51,否,否
180105.SZ,易方达广州开发区高新产业园REIT,2024-09-23,园区基础设施,广州开发区控股集团有限公司,地方国有企业,广州凯云发展股份有限公司,子公司,基础运营管理费基础运营管理费=RE×5.8%，其中，RE=项目公司当年经审计的实际运营收入（以项目公司年度审计报告为准，计提及支付期间不满一年的，按照对应期间的审计数据计提）,"自 2025 年 1 月 1 日（含）起的绩效运营管理费计算方法如下：M=RE×R 其中，M=外部管理机构在当年应提取的绩效运营管理费；R=绩效运营管理费费率，R 的取值根据 X 的计算结果确认；X=运营收入净额实际值/运营收入净额目标值；RE=项目公司当年经审计的实际运营收入（以项目公司年度审计报告为准，计提及支付期间不满一年的，按照对应期间的审计数据计提）。绩效运营管理费费率取值安排：X>120%，4%；110%＜X≤120%，2%；90%≤X≤110%，0%；80%≤X＜90%，-2%；X<80%，-4%。运营管理费的具体计算及支付安排，按照招募说明书、运营管理协议的详细约定执行。","基金固定管理费自基金合同生效日起，基金固定管理费率为 0.40%/年，其中 0.30%由基金管理人收取，0.1%由计划管理人收取。",0.0001,,0.35,否,否
180106.SZ,广发成都高投产业园REIT,2024-12-19,园区基础设施,成都高投置业有限公司,地方国有企业,"成都天府软件园有限公司,成都高投资产经营管理有限公司",,"基础运营管理服务费=当期项目公司实际运营收入×10%",浮动运营管理服务费=（当期项目公司实际净运营收入-当期项目公司目标净运营收入）×R。浮动运营管理服务费的奖励或扣减金额不超过当期基础运营管理服务费金额的 80%。R 为计算浮动运营管理服务费的费率，不同考核结果对应的浮动运营管理服务费的费率安排：净运营收入完成率≥115%，40%；115%＞净运营收入完成率≥105%，20%；105%＞净运营收入完成率≥95%，0；95%＞净运营收入完成率≥85%，20%；85%＞净运营收入完成率，40%,基金固定管理费的年费率为：0.20%，其中 0.16% 由基金管理人收取，0.04%由计划管理人收取。,0.0001,,0.34,,
508000.SH,华安张江产业园REIT,2021-06-21,园区基础设施,光控安石(北京)投资管理有限公司,地方国有企业,上海集挚咨询管理有限公司,,"本基金固定管理费（H）的计算方法如下：H＝H1+H2。H1 为首发项目张江光大园每日应计提的固定管理费，计算公式为：H1=E×P1×0.55%÷当年天数。H2 为扩募项目张润大厦每日应计提的固定管理费，计算公式为：H2=E×P2×0.15%÷当年天数","本基金浮动管理费（F）的计算方法如下：F= F1+F2。F1 为运营管理首发项目张江光大园每年应支付的浮动管理费，最低为 0，不得为负，其计算公式如下：F1=Max【(首发项目张江光大园之物业资产运营收入回收期 内实际收到的物业资产运营净收入-该物业资产运营收入回收期对应目标金额 ）,0】×10%+上一自然年度 首发项目张江光大园的物业资产运营收入总额 ×4%- E×P1×0.2% ×该物业资产运营收入回收期实际天数/当年总天数。F2 为运营管理扩募项目张润大厦每年应支付的浮动管理费，最低为 0，不得为负，其计算公式如下：F2=Max【（扩募项目张润大厦之物业资产运营收入回收期内实际收到的物业资产运营净收入-该物业资产运营收入回收期对应目标金额），0】×10%+对应期间 扩募项目张润大厦的物业资产运营收入总额×7.5%。","0.01%",,0.271901528,,
508003.SH,中金联东科技创新产业园REIT,2024-11-05,园区基础设施,北京联东金园管理科技有限公司,民营企业,北京联东金园管理科技有限公司,,（1）基础管理费用=I×9.0%；I表示项目公司当年经审计的营业收入，以项目公司年度审计报告为准；,（2）浮动管理费用=（C-T）×R。预算实现率≤90% R=20%;90%<预算实现率<110% R=10%;110%≤预算实现率 R=20%;特别地，当C＜T时，将相应扣减基础管理费用，扣减上限不超过外部管理机构当期确认的基础管理费用金额的80%。,固定管理费用的85%由基金管理人收取，固定管理费用的15%由划管理人收取。固定管理费用的计算方法如下：H=E×0.22%/当年天数,"0.01%",,0.34,,
508010.SH,中金重庆两江产业园REIT,2024-12-11,园区基础设施,重庆两江新区产业发展集团有限公司,地方国有企业,重庆两江新区产业发展集团有限公司,,基础管理费用=Ix9.5%；I表示项目公司当年经审计的营业收入，以项目公司年度审计报告为准。,浮动管理费用=（C-T）×15%；特别地，当C＜T时，将相应扣减基础管理费用，扣减上限不超过运营管理机构当期确认的基础管理费用金额的80%。,固定管理费用中85%由基金管理人收取，15%由计划管理人收取。固定管理费用的计算方法如下：H=E×0.20%/当年天数；,"0.01%",,0.49,,
508012.SH,招商科创孵化器REIT,2024-12-31,园区基础设施,上海杨浦科技创业中心有限公司,0,上海杨浦科技创业中心有限公司,,,,"基金成立首个自然年度及第二个自然年度的固定管理费按最近一期年度报告披露的基金资产净值（首次年度报告披露之前为募集规模（含募集期利息））的 0.30%的年费率计提，其中 0.20%由基金管理人收取、0.10%由计划管理人收取。基金成立第二个自然年度之后的固定管理费按最近一期年度报告披露的基金资产净值（首次年度报告披露之前为募集规模（含募集期利息））的 0.50%的年费率计提，其中 0.40%由基金管理人收取、0.10%由计划管理人收取。","0.01%",,0.307291,,
508019.SH,中金湖北科投光谷产业园REIT,2023-06-30,园区基础设施,湖北省科技投资集团有限公司,0,武汉光谷资产投资管理有限公司,,基础管理费用=I×5.0%,浮动管理费用=（C-T）×15%。,"固定管理费用的85%由基金管理人收取，15%由计划管理人收取。固定管理费用的计算方法如下：H=E×0.2%÷当年天数",,,0.34,,
508021.SH,国泰君安临港创新产业园REIT,2022-10-13,园区基础设施,上海临港奉贤经济发展有限公司,地方国有企业,"上海临港奉贤经济发展有限公司,上海临港经济发展(集团)有限公司",,,,,,,0.2,,
508022.SH,博时津开科工产业园REIT,2024-09-19,园区基础设施,天津泰达科技工业园有限公司,地方国有企业,天津泰да产业园管理有限公司,,,,,,,0.34,,
508027.SH,东吴苏州工业园区产业园REIT,2021-06-21,园区基础设施,苏州工业园区建屋产业园开发有限公司,地方国有企业,"苏州工业园区建屋产业园开发有限公司,苏州工业园区科技发展有限公司",,,,,,,0.4,,
508039.SH,创金合信首农REIT,2025-07-25,园区基础设施,北京首农信息产业投资有限公司,地方国有企业,北京首农信息产业投资有限公司,,,,,,,0.51,,
508080.SH,中金亦庄产业园REIT,2025-06-26,园区基础设施,北京亦庄盛元投资开发集团有限公司,地方国有企业,北京盛芯运营管理有限公司,,,,,,,0.25,,
508088.SH,国泰君安东久新经济REIT,2022-10-14,园区基础设施,FULL REGALIA LIMITED,外资企业,东久(上海)投资管理咨询有限公司,,,,,,,0.2,,
508092.SH,华夏金隅智造工场REIT,2025-02-26,园区基础设施,北京金隅集团股份有限公司,地方国有企业,北京金隅文化科技发展有限公司,,,,,,,0.35,,
508097.SH,华泰紫金南京建邺产业园REIT,2024-12-03,园区基础设施,南京市建邺区高新科技投资集团有限公司,地方国有企业,南京市建邺区高新科技投资集团有限公司,,,,,,,0.35,,
508099.SH,建信中关村产业园REIT,2021-12-17,园区基础设施,北京中关村软件园发展有限责任公司,地方国有企业,北京中关村软件园发展有限责任公司,同一主体,,,,,,0.3334,否,不适用
    </script>

    <script type="module">
        // --- Data Store ---
        const MOCK_DATA = {
            reits: [],
            daily: {}, 
            financials_q: {}, 
            financials_stm: {}, 
            valuation: {}, 
            detailed_info: {}, 
            risk_warning: [], 
            asset_valuation: {}
        };
        let comparisonRadarChart = null;
        let riskVisualizationChart = null;

        const risk_warning_data = `代码,证券名称,买入均价,现价,持仓金额（万元）,持仓集中度,VAR值,持仓收益率,是否触发预警线,是否触发止损线
180103.SZ,华夏和达高科REIT,2.244,2.307,117,0.12,0.028,0.028074866,否,否
508000.SH,华安张江产业园REIT,2.767,2.748,206,0.22,0.035,-0.006866643,否,否
508019.SH,中金湖北科投光谷REIT,2.611,2.039,103,0.11,0.042,-0.219073152,是,否
508027.SH,东吴苏园产业REIT,3.36,3.17,90,0.09,0.021,-0.056547619,否,否
508099.SH,建信中关村REIT,2.069,2.098,155,0.16,0.015,0.014016433,否,否
508039.SH,创金合信首农REIT,4.738,4.89,188,0.20,0.018,0.032081047,否,否
508080.SH,中金亦庄产业园REIT,3.967,3.736,146,0.15,0.025,-0.058230401,否,否`;

        // --- Data Configuration for Comparison Grid ---
        const fieldConfig = [
            {
                category: "basic", name: "基础信息",
                fields: [
                    { id: "代码", name: "证券代码", type: "text" },
                    { id: "REITs上市日期", name: "上市日期", type: "date" },
                    { id: "资产类型", name: "资产类型", type: "text" },
                ]
            },
            {
                category: "operation", name: "运营管理",
                fields: [
                    { id: "原始权益人", name: "原始权益人", type: "text" },
                    { id: "原始权益人性质", name: "权益人性质", type: "text" },
                    { id: "运营管理机构", name: "运营管理机构", type: "text" },
                ]
            },
            {
                category: "financial", name: "核心费率与份额",
                fields: [
                    { id: "管理人报酬", name: "管理人报酬", type: "text" },
                    { id: "托管费", name: "托管费", type: "number" },
                    { id: "原始权益人或其同一控制下关联方配售份额占总份额比例(首发)", name: "首发配售比例", type: "percent" }
                ]
            },
            {
                category: "expansion", name: "扩募动态",
                fields: [
                    { id: "是否发布扩募公告", name: "发布扩募公告", type: "status" },
                    { id: "扩募是否获得批准", name: "扩募批准状态", type: "text" },
                ]
            }
        ];


        // --- CSV Parsing and Data Initialization ---
        function parseCSV(text) {
            const lines = text.trim().split(/\r?\n/);
            if (lines.length < 2) return [];
            const headers = lines[0].split(',').map(h => h.trim());
            return lines.slice(1).map(line => {
                const values = line.split(',');
                return headers.reduce((acc, header, index) => {
                    acc[headers[index]] = values[index] ? values[index].trim() : '';
                    return acc;
                }, {});
            });
        }

        function generateDailyData() {
            const data = { labels: [], prices: [], turnovers: [] };
            let price = 2.5 + (Math.random() - 0.5);
            for (let i = 240; i > 0; i--) {
                const date = new Date('2025-08-25T00:00:00'); date.setDate(date.getDate() - i);
                data.labels.push(date.toISOString().split('T')[0]);
                price *= (1 + (Math.random() - 0.5) * 0.03);
                data.prices.push(parseFloat(price.toFixed(2)));
                data.turnovers.push(parseFloat((Math.random() * 2 + 0.5).toFixed(2)));
            }
            return data;
        }

        function generateFinancials() {
            const q_labels = ['2024Q2', '2024Q3', '2024Q4', '2025Q1', '2025Q2'];
            const q_data = { labels: q_labels, datasets: {} };
            const stm_data = { labels: ['2023H2', '2024H1', '2024H2'], datasets: {} };
            MOCK_DATA.reits.forEach(reit => {
                const reitId = reit.id.split('.')[0];
                q_data.datasets[reitId] = {}; stm_data.datasets[reitId] = {};
                q_labels.forEach(p => {
                    const income = 2000 + Math.random() * 600, ri = income * (0.8 + Math.random() * 0.1), pri = income * (0.05 + Math.random() * 0.05), pi = income - ri - pri;
                    q_data.datasets[reitId][p] = { ebit: 1500 + Math.random() * 500, distributed: 1200 + Math.random() * 400, income, ri, pri, pi };
                });
                stm_data.labels.forEach(p => { stm_data.datasets[reitId][p] = { op_revenue: 4000 + Math.random() * 1000, op_cost: 800 + Math.random() * 200, depreciation: 200 + Math.random() * 50, tax: 50 + Math.random() * 20, manage_fee: 100 + Math.random() * 50 }; });
            });
            MOCK_DATA.financials_q = q_data; MOCK_DATA.financials_stm = stm_data;
        }

        // --- Chart and Table Update Functions ---
        let priceTurnoverChart, financialsChartQ, financialsChartStm, bubbleChart, radarChart, incomeChart, assetValuationChart;
        let ebitdaChart, distributionChart, growthChart, structureChart, costChart;
        let assetsData = [];
        let occupancyChart, rentChart, concentrationChart;
        const PALETTE = ['#004AAD', '#0094D1', '#78C5E8', '#F7931E', '#F15A24', '#D4145A'];
        
        function getSelectedReits() {
            const globalSelectorPanel = document.getElementById('global-selector-panel');
            if (!globalSelectorPanel) {
                console.error('Global selector panel not found!');
                return [];
            }
            return Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
        }

        function createDateSlider(containerId, allLabels, onChangeCallback) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="date-slider-container"><div class="slider-track"><div class="slider-range"></div><div class="slider-handle" data-handle="start"></div><div class="slider-handle" data-handle="end"></div></div><div class="date-labels"><span class="start-date-label"></span><span class="end-date-label"></span></div></div>`;
            const track = container.querySelector('.slider-track'), range = container.querySelector('.slider-range'), startHandle = container.querySelector('[data-handle="start"]'), endHandle = container.querySelector('[data-handle="end"]'), startDateLabel = container.querySelector('.start-date-label'), endDateLabel = container.querySelector('.end-date-label');
            let min = 0, max = allLabels.length - 1, currentMin = max > 90 ? max - 90 : min, currentMax = max;
            const updateSliderUI = () => {
                if(allLabels.length === 0) return;
                const minPercent = (currentMin / max) * 100, maxPercent = (currentMax / max) * 100;
                startHandle.style.left = `calc(${minPercent}% - 8px)`; endHandle.style.left = `calc(${maxPercent}% - 8px)`;
                range.style.left = `${minPercent}%`; range.style.width = `${maxPercent - minPercent}%`;
                startDateLabel.textContent = allLabels[currentMin]; endDateLabel.textContent = allLabels[currentMax];
            };
            const makeDraggable = (handle) => {
                handle.onmousedown = (e) => {
                    e.preventDefault();
                    document.onmousemove = (moveEvent) => {
                        const rect = track.getBoundingClientRect(); let newPos = (moveEvent.clientX - rect.left) / rect.width; newPos = Math.max(0, Math.min(1, newPos));
                        const newIndex = Math.round(newPos * max);
                        if (handle.dataset.handle === 'start') { currentMin = Math.min(newIndex, currentMax - 1); } else { currentMax = Math.max(newIndex, currentMin + 1); }
                        updateSliderUI();
                    };
                    document.onmouseup = () => { document.onmousemove = document.onmouseup = null; onChangeCallback({ startIndex: currentMin, endIndex: currentMax }); };
                };
            };
            makeDraggable(startHandle); makeDraggable(endHandle); updateSliderUI();
            return { getRange: () => ({ startIndex: currentMin, endIndex: currentMax }) };
        }

        const commonChartOptions = { responsive: true, maintainAspectRatio: false, interaction: { mode: 'index', intersect: false }, plugins: { legend: { position: 'top' } } };

        // --- NEW/UPDATED FUNCTIONS FOR COMPARISON GRID ---
        function renderComparisonGrid(selectedIds) {
            const container = document.getElementById('comparison-grid');
            container.innerHTML = '';
            if (selectedIds.length === 0) return;

            container.style.gridTemplateColumns = `150px repeat(${selectedIds.length}, minmax(200px, 1fr))`;

            const headerRow = document.createElement('div');
            headerRow.className = 'comparison-header-row';
            const cornerCell = document.createElement('div');
            cornerCell.className = 'comparison-header-cell';
            headerRow.appendChild(cornerCell);

            selectedIds.forEach(id => {
                const headerCell = document.createElement('div');
                headerCell.className = 'comparison-header-cell';
                headerCell.textContent = MOCK_DATA.detailed_info[id]?.['证券简称'] || id;
                headerRow.appendChild(headerCell);
            });
            container.appendChild(headerRow);

            const activeCategory = document.getElementById('category-filter').value;
            fieldConfig.forEach(config => {
                if (activeCategory !== 'all' && activeCategory !== config.category) return;

                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'comparison-category-header';
                categoryHeader.textContent = config.name;
                categoryHeader.dataset.category = config.category;
                container.appendChild(categoryHeader);

                config.fields.forEach(field => {
                    const rowContainer = document.createElement('div');
                    rowContainer.className = 'comparison-header-row';

                    const fieldNameCell = document.createElement('div');
                    fieldNameCell.className = 'comparison-header-cell';
                    fieldNameCell.dataset.role = 'field-name';
                    fieldNameCell.textContent = field.name;
                    rowContainer.appendChild(fieldNameCell);

                    selectedIds.forEach(id => {
                        const dataCell = document.createElement('div');
                        dataCell.className = 'comparison-data-cell';
                        dataCell.dataset.field = field.id;
                        dataCell.dataset.reitId = id;
                        const reitData = MOCK_DATA.detailed_info[id];
                        const value = reitData ? reitData[field.id] : 'N/A';

                        switch (field.type) {
                            case 'percent':
                                const numValue = parseFloat(value);
                                if (!isNaN(numValue)) {
                                    const percentValue = (numValue * 100).toFixed(2) + '%';
                                    dataCell.innerHTML = `<span>${percentValue}</span><div class="progress-bar"><div class="progress-fill" style="width: ${numValue * 100}%"></div></div>`;
                                } else {
                                    dataCell.textContent = 'N/A';
                                }
                                break;
                            case 'status':
                                let badgeClass = '';
                                let statusText = value;
                                if (value === '是') { badgeClass = 'yes'; }
                                else if (value === '否') { badgeClass = 'no'; }
                                dataCell.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;
                                break;
                            default:
                                dataCell.textContent = value;
                        }
                        rowContainer.appendChild(dataCell);
                    });
                    container.appendChild(rowContainer);
                });
            });
            highlightDifferences(selectedIds);
        }

        function highlightDifferences(selectedIds) {
            if (!document.getElementById('toggle-differences').classList.contains('active')) {
                return;
            }
            fieldConfig.forEach(config => {
                config.fields.forEach(field => {
                    if (field.type !== 'number' && field.type !== 'percent') return;
                    
                    const values = selectedIds
                        .map(id => parseFloat(MOCK_DATA.detailed_info[id]?.[field.id]))
                        .filter(v => !isNaN(v));

                    if (values.length < 2) return;

                    const max = Math.max(...values);
                    const min = Math.min(...values);

                    if (max !== min) {
                        selectedIds.forEach(id => {
                            const value = parseFloat(MOCK_DATA.detailed_info[id]?.[field.id]);
                            if (value === max || value === min) {
                                const cell = document.querySelector(`.comparison-data-cell[data-field="${field.id}"][data-reit-id="${id}"]`);
                                if (cell) cell.classList.add('highlight');
                            }
                        });
                    }
                });
            });
        }
        
        // --- Other update functions ---

        function updatePerformanceTable(selectedIds) {
             const perfTable = document.getElementById('performance-table');
             let perfTableHTML = `<thead><tr><th>标的</th><th>周涨跌幅</th><th>月涨跌幅</th><th>年初至今</th></tr></thead><tbody>`;
             selectedIds.forEach(id => {
                 const reitId = id.split('.')[0];
                 const dailyData = MOCK_DATA.daily[reitId];
                 if (!dailyData) return;

                 const p = dailyData.prices, l = dailyData.labels;
                 if (p.length === 0) return;

                 const today = p[p.length - 1], week = p[p.length - 8] || p[0], month = p[p.length - 31] || p[0];
                 const ytdIdx = l.findIndex(d => d.startsWith('2025-01')), ytd = ytdIdx > -1 ? p[ytdIdx] : p[0];
                 const wc = ((today / week - 1) * 100).toFixed(2), mc = ((today / month - 1) * 100).toFixed(2), yc = ((today / ytd - 1) * 100).toFixed(2);
                 perfTableHTML += `<tr><td>${MOCK_DATA.reits.find(r=>r.id===id).name}</td><td>${wc}%</td><td>${mc}%</td><td>${yc}%</td></tr>`;
             });
             perfTable.innerHTML = perfTableHTML + '</tbody>';
        }

        function updateFinancials(selectedIds) {
            if (selectedIds.length === 0) return;

            // Initialize ECharts if not already done
            if (!ebitdaChart) {
                try {
                    const ebitdaChartEl = document.getElementById('ebitda-chart');
                    if (ebitdaChartEl) ebitdaChart = echarts.init(ebitdaChartEl);
                    const distributionChartEl = document.getElementById('distribution-chart');
                    if (distributionChartEl) distributionChart = echarts.init(distributionChartEl);
                    const growthChartEl = document.getElementById('growth-chart');
                    if (growthChartEl) growthChart = echarts.init(growthChartEl);
                    const structureChartEl = document.getElementById('structure-chart');
                    if (structureChartEl) structureChart = echarts.init(structureChartEl);
                    const costChartEl = document.getElementById('cost-chart');
                    if (costChartEl) costChart = echarts.init(costChartEl);
                } catch(e) {
                    console.error('ECharts initialization failed', e);
                }
            }

            try { updateEbitdaChart(selectedIds); } catch(e) { console.error('updateEbitdaChart failed', e); }
            try { updateDistributionChart(selectedIds); } catch(e) { console.error('updateDistributionChart failed', e); }
            try { updateGrowthChart(selectedIds); } catch(e) { console.error('updateGrowthChart failed', e); }
            try { updateStructureChart(selectedIds); } catch(e) { console.error('updateStructureChart failed', e); }
            try { updateCostChart(selectedIds); } catch(e) { console.error('updateCostChart failed', e); }
        }
        
        function updateIncomeStructureChart(selectedIds) {
            // This function is currently empty as per the request.
            // You can add content here in the future.
        }


        function updatePriceTurnoverChart(selectedIds, range) {
            const canvas = document.getElementById('price-turnover-chart');
            if (!canvas) { console.error('Canvas for price chart not found'); return; }
            if (selectedIds.length === 0) { if (priceTurnoverChart) priceTurnoverChart.destroy(); return; }

            const firstReitId = selectedIds[0].split('.')[0];
            if (!MOCK_DATA.daily[firstReitId]) return;

            const { startIndex, endIndex } = range;
            const labels = MOCK_DATA.daily[firstReitId].labels.slice(startIndex, endIndex + 1);
            const datasets = [];
            selectedIds.forEach((id, index) => {
                const reitId = id.split('.')[0];
                const color = PALETTE[index % PALETTE.length];
                const reitName = MOCK_DATA.reits.find(r=>r.id===id).name;
                datasets.push({ type: 'line', label: `${reitName} 价格`, data: MOCK_DATA.daily[reitId].prices.slice(startIndex, endIndex + 1), borderColor: color, yAxisID: 'yPrice', tension: 0.1, pointRadius: 0, borderWidth: 2 });
                datasets.push({ type: 'bar', label: `${reitName} 换手率`, data: MOCK_DATA.daily[reitId].turnovers.slice(startIndex, endIndex + 1), backgroundColor: color + '40', yAxisID: 'yTurnover' });
            });
            if (priceTurnoverChart) priceTurnoverChart.destroy();
            priceTurnoverChart = new Chart(canvas, { 
                type: 'bar', 
                data: { labels, datasets }, 
                options: { 
                    ...commonChartOptions, 
                    scales: { 
                        yPrice: { type: 'linear', position: 'left', title: { display: true, text: '价格 (元)' } }, 
                        yTurnover: { type: 'linear', position: 'right', title: { display: true, text: '换手率 (%)' }, grid: { drawOnChartArea: false } } 
                    },
                    plugins: {
                        ...commonChartOptions.plugins,
                        legend: {
                            labels: {
                                filter: (item) => item.text.includes('价格')
                            }
                        }
                    }
                } 
            });
        }

        function updateBubbleChart(selectedIds) {
            const canvas = document.getElementById('reits-bubble-chart');
            if (!canvas) { console.error('Canvas for bubble chart not found'); return; }
            const xDim = document.getElementById('bubble-x-axis').value, yDim = document.getElementById('bubble-y-axis').value, sizeDim = document.getElementById('bubble-size').value;
            
            const chartData = selectedIds.map(id => {
                const reitInfo = MOCK_DATA.reits.find(r => r.id === id);
                if (!reitInfo) return null;

                // Generate mock data on the fly to ensure availability
                const mockValuation = { 
                    pffo: 20 + Math.random() * 10, 
                    dist_ratio: 3 + Math.random() * 3, 
                    mv: 20 + Math.random() * 20, 
                    nav_premium: (Math.random() - 0.2) * 20 
                };

                return {
                    x: mockValuation[xDim],
                    y: mockValuation[yDim],
                    r: mockValuation[sizeDim] / 2,
                    name: reitInfo.name
                };
            }).filter(d => d !== null);

            const datasets = [{ label: '园区REITs', data: chartData, backgroundColor: PALETTE.map(c => c + '80') }];
            if (bubbleChart) bubbleChart.destroy();
            bubbleChart = new Chart(canvas, { type: 'bubble', data: { datasets }, options: { ...commonChartOptions, scales: { x: { title: { display: true, text: document.querySelector(`#bubble-x-axis option[value=${xDim}]`).textContent } }, y: { title: { display: true, text: document.querySelector(`#bubble-y-axis option[value=${yDim}]`).textContent } } }, plugins: { legend: { display: false }, tooltip: { callbacks: { label: (c) => `${c.raw.name}: ${c.raw.x.toFixed(2)}, ${c.raw.y.toFixed(2)}, Size: ${(c.raw.r*2).toFixed(1)}` } } } } });
        }

        function updateRadarChart(selectedIds) {
            const canvas = document.getElementById('radar-chart');
            if (!canvas) { console.error('Canvas for radar chart not found'); return; }
            const selectors = document.querySelectorAll('#radar-metrics-selectors select');
            const selectedMetrics = Array.from(selectors).map(s => s.value);
            const labels = Array.from(selectors).map(s => s.options[s.selectedIndex].text);
            const metricNormalizers = { nav_premium: (v) => 10 - (v / 5), dist_ratio: (v) => v * 1.5, pffo: (v) => v / 3, mv: (v) => v / 5 };
            
            const datasets = selectedIds.map((id, index) => {
                const reitInfo = MOCK_DATA.reits.find(r => r.id === id);
                if (!reitInfo) return null;

                // Generate mock data on the fly
                const mockValuation = { 
                    pffo: 20 + Math.random() * 10, 
                    dist_ratio: 3 + Math.random() * 3, 
                    mv: 20 + Math.random() * 20, 
                    nav_premium: (Math.random() - 0.2) * 20 
                };

                return {
                    label: reitInfo.name,
                    data: selectedMetrics.map(metric => metricNormalizers[metric](mockValuation[metric])),
                    borderColor: PALETTE[index % PALETTE.length],
                    backgroundColor: PALETTE[index % PALETTE.length] + '40',
                };
            }).filter(d => d !== null);

            if (radarChart) radarChart.destroy();
            radarChart = new Chart(canvas, { type: 'radar', data: { labels, datasets }, options: { ...commonChartOptions, scales: { r: { suggestedMin: 0, suggestedMax: 10 } } } });
        }

        // --- 新的资产运营模块函数 ---
        let selectedAssets = [];
        let multiOccupancyChart, multiRentChart, multiIndustryChart;

        // 基于真实CSV数据的资产运营数据
        const assetOperationsData = `基金代码,基金简称,资产名称,类型,资产所在地,产业园区,2024年期末出租率(%),2023年期末出租率(%),2022年期末出租率(%),2021年期末出租率(%),2024年期内租金单价水平（元/平方米/月）,2023年期内租金单价水平（元/平方米/月）,2022年期内租金单价水平（元/平方米/月）,2021年期内租金单价水平（元/平方米/月）,2024年可租赁面积(万平方米), 2024年租户集中度 （前五大占比）,2024年剩余租期,2024年行业分布
180101.SZ,博时招商蛇口产业园REIT,万海大厦,办公,深圳市南山区,蛇口网谷产业园,93.66,91,81,88,124.68,130.54,140.74,-,5.26,6.62,1.48,新一代信息技术、现代服务业、商业、数字文创
180101.SZ,博时招商蛇口产业园REIT,万融大厦,办公,深圳市南山区,蛇口网谷产业园,88.96,92,84,88,114.81,120.46,119.01,-,4.14,6.46,1.03,
180101.SZ,博时招商蛇口产业园REIT,招商局光明科技园科技企业加速器二期,办公+厂房,深圳市光明区,招商局光明科技园,90.5,97,97.1,,57.52,59.36,-,-,11.07,8.31,1.35,高端制造、生物产业、创新创业服务、新材料新能源
508000.SH,华安张江产业园REIT,张江光大园项目,办公,上海市浦东新区,张江光大园,91.37,80.68,92.6,100,164.4,159.3,-,-,4.32,36.4,1.93,集成电路：41.74%；TMT：21.74%；金融科技：13.81%；先进制造业：11.66%
508000.SH,华安张江产业园REIT,张润大厦,办公,上海市浦东新区,张江集成电路设计产业园,92.44,63.31,96.32,,167.1,165.9,177,174.3,4.31,36.4,2.37,
508027.SH,东吴苏州工业园区产业园REIT,国际科技园五期B区项目,办公,苏州市吴中区,中新生态科技城,79.82,93.2,95.59,93.22,43.8,43.2,27.45,38.89,26.96,15.93,1.52,信息技术：35.36%；人工智能：29.25%；集成电路：12.24%
508027.SH,东吴苏州工业园区产业园REIT,"2.5产业园一期,二期项目",办公,苏州市吴中区,国际科技园,82.66,85.02,85.18,74.19,62.4,62.1,58.73,48.25,15.53,24.31,1.83,信息技术：26.43%；电子产业：17.20%；生物医药：14.85%
508099.SH,建信中关村产业园REIT,孵化加速器项目,办公,北京市海淀区,中关村软件园,62.98,64.52,81.29,100,127.5,145.48,-,-,3.97,44.39,2.58,-
508099.SH,建信中关村产业园REIT,互联网创新中心5号楼项目,办公,北京市海淀区,中关村软件园,66.99,51.56,,90,127.2,151.5,-,-,6.3,44.39,2.96,`;

        let parsedAssetsData = [];

        function parseAssetOperationsCSV() {
            const rows = assetOperationsData.trim().split('\n').slice(1);
            parsedAssetsData = rows.map(row => {
                const cols = row.split(',');

                const occupancyHistory = [
                    { year: '2021', value: parseFloat(cols[9]) || null },
                    { year: '2022', value: parseFloat(cols[8]) || null },
                    { year: '2023', value: parseFloat(cols[7]) || null },
                    { year: '2024', value: parseFloat(cols[6]) || null },
                ].filter(d => d.value !== null && !isNaN(d.value));

                const rentHistory = [
                    { year: '2021', value: parseFloat(cols[13]) || null },
                    { year: '2022', value: parseFloat(cols[12]) || null },
                    { year: '2023', value: parseFloat(cols[11]) || null },
                    { year: '2024', value: parseFloat(cols[10]) || null },
                ].filter(d => d.value !== null && !isNaN(d.value));

                return {
                    code: cols[0] || '',
                    name: cols[1] || '',
                    asset: cols[2] || '',
                    type: cols[3] || '',
                    location: cols[4] || '',
                    park: cols[5] || '',
                    occupancyHistory,
                    rentHistory,
                    area: parseFloat(cols[14]) || null,
                    topTenantsRatio: parseFloat(cols[15]) || null,
                    leaseTerm: parseFloat(cols[16]) || null,
                    industryDistributionRaw: cols[17] || ''
                };
            }).filter(asset => asset.code && asset.name && asset.asset);
        }

        function updateAssetOperations(selectedIds) {
            updateAssetSelector(selectedIds);
            updateAssetAnalysis();
        }

        function updateAssetSelector(selectedIds) {
            const container = document.getElementById('asset-selector-container');
            const countElement = document.getElementById('selected-reits-count');
            const totalAssetsElement = document.getElementById('total-assets-count');

            if (!container) return;

            // 获取选中REITs对应的资产
            const availableAssets = parsedAssetsData.filter(asset => selectedIds.includes(asset.code));
            const groupedAssets = {};

            availableAssets.forEach(asset => {
                if (!groupedAssets[asset.code]) {
                    groupedAssets[asset.code] = {
                        code: asset.code,
                        name: asset.name,
                        assets: []
                    };
                }
                groupedAssets[asset.code].assets.push(asset);
            });

            // 更新计数
            countElement.textContent = selectedIds.length;
            totalAssetsElement.textContent = availableAssets.length;

            // 生成资产选择器
            container.innerHTML = '';
            Object.values(groupedAssets).forEach(reit => {
                const reitDiv = document.createElement('div');
                reitDiv.className = 'bg-white border rounded-lg p-3';
                reitDiv.innerHTML = `
                    <h4 class="font-semibold text-sm text-gray-800 mb-2">${reit.code} ${reit.name}</h4>
                    <div class="space-y-1">
                        ${reit.assets.map(asset => `
                            <label class="flex items-center text-xs">
                                <input type="checkbox" value="${asset.code}|${asset.asset}" class="asset-checkbox mr-2">
                                <span class="text-gray-700">${asset.asset}</span>
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(reitDiv);
            });

            container.querySelectorAll('.asset-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedAssets);
            });
        }

        function updateSelectedAssets() {
            const checkboxes = document.querySelectorAll('.asset-checkbox:checked');
            selectedAssets = Array.from(checkboxes).map(cb => {
                const [code, assetName] = cb.value.split('|');
                return parsedAssetsData.find(asset => asset.code === code && asset.asset === assetName);
            }).filter(Boolean);

            updateAssetAnalysis();
        }

        function updateAssetAnalysis() {
            const initialMessage = document.getElementById('asset-initial-message');
            const chartsContainer = document.getElementById('asset-charts-container');
            const tableContainer = document.getElementById('asset-data-table-container');

            if (selectedAssets.length === 0) {
                initialMessage.classList.remove('hidden');
                chartsContainer.classList.add('hidden');
                tableContainer.classList.add('hidden');
                return;
            }

            initialMessage.classList.add('hidden');
            chartsContainer.classList.remove('hidden');
            tableContainer.classList.remove('hidden');

            updateAssetMetrics();
            updateAssetCharts();
            updateAssetDataTable();
        }

        function updateAssetMetrics() {
            const avgOccupancy = selectedAssets.reduce((sum, asset) => {
                const latest = asset.occupancyHistory[asset.occupancyHistory.length - 1];
                return sum + (latest ? latest.value : 0);
            }, 0) / selectedAssets.length;

            const avgRent = selectedAssets.reduce((sum, asset) => {
                const latest = asset.rentHistory[asset.rentHistory.length - 1];
                return sum + (latest ? latest.value : 0);
            }, 0) / selectedAssets.length;

            const totalArea = selectedAssets.reduce((sum, asset) => sum + (asset.area || 0), 0);
            const avgLeaseTerm = selectedAssets.reduce((sum, asset) => sum + (asset.leaseTerm || 0), 0) / selectedAssets.length;

            document.getElementById('avg-occupancy').textContent = avgOccupancy.toFixed(1) + '%';
            document.getElementById('avg-rent').textContent = avgRent.toFixed(0);
            document.getElementById('total-area').textContent = totalArea.toFixed(1);
            document.getElementById('avg-lease-term').textContent = avgLeaseTerm.toFixed(1);
        }

        function updateAssetCharts() {
            updateMultiOccupancyChart();
            updateMultiRentChart();
            updateMultiIndustryChart();
        }

        function updateMultiOccupancyChart() {
            const canvas = document.getElementById('multi-occupancy-chart');
            if (!canvas) return;

            if (multiOccupancyChart) multiOccupancyChart.destroy();

            const datasets = selectedAssets.map((asset, index) => ({
                label: asset.asset,
                data: asset.occupancyHistory.sort((a, b) => a.year.localeCompare(b.year)).map(d => ({ x: d.year, y: d.value })),
                borderColor: `hsl(${index * 360 / selectedAssets.length}, 70%, 50%)`,
                backgroundColor: `hsla(${index * 360 / selectedAssets.length}, 70%, 50%, 0.1)`,
                tension: 0.1
            }));

            multiOccupancyChart = new Chart(canvas, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { type: 'category', title: { display: true, text: '年份' } },
                        y: { title: { display: true, text: '出租率 (%)' }, suggestedMin: 70, suggestedMax: 100 }
                    },
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        function updateMultiRentChart() {
            const canvas = document.getElementById('multi-rent-chart');
            if (!canvas) return;

            if (multiRentChart) multiRentChart.destroy();

            const datasets = selectedAssets.map((asset, index) => ({
                label: asset.asset,
                data: asset.rentHistory.sort((a, b) => a.year.localeCompare(b.year)).map(d => ({ x: d.year, y: d.value })),
                borderColor: `hsl(${index * 360 / selectedAssets.length}, 70%, 50%)`,
                backgroundColor: `hsla(${index * 360 / selectedAssets.length}, 70%, 50%, 0.1)`,
                tension: 0.1
            }));

            multiRentChart = new Chart(canvas, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { type: 'category', title: { display: true, text: '年份' } },
                        y: { title: { display: true, text: '租金 (元/㎡/月)' } }
                    },
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        let industryChartInstances = [];
        function updateMultiIndustryChart() {
            const container = document.getElementById('multi-industry-container');
            if (!container) return;

            // Clear previous charts and instances
            industryChartInstances.forEach(chart => chart.destroy());
            industryChartInstances = [];
            container.innerHTML = '';

            if (selectedAssets.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 col-span-full">请选择资产以查看行业分布。</p>';
                return;
            }

            selectedAssets.forEach((asset, index) => {
                const chartWrapper = document.createElement('div');
                chartWrapper.className = 'relative h-64';
                const canvas = document.createElement('canvas');
                canvas.id = `industry-chart-${index}`;
                chartWrapper.appendChild(canvas);
                container.appendChild(chartWrapper);

                const industryData = parseIndustryDistribution(asset.industryDistributionRaw);
                const labels = Object.keys(industryData);
                const data = Object.values(industryData);

                const newChart = new Chart(canvas, {
                    type: 'doughnut',
                    data: {
                        labels: labels.length > 0 ? labels : ['无数据'],
                        datasets: [{
                            data: data.length > 0 ? data : [100],
                            backgroundColor: data.length > 0 ? PALETTE : ['#E5E7EB'],
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: labels.length > 0, position: 'bottom', labels: { font: { size: 10 } } },
                            title: { display: true, text: asset.asset, font: { size: 14 } }
                        }
                    }
                });
                industryChartInstances.push(newChart);
            });
        }

        function parseIndustryDistribution(rawString) {
            if (!rawString || typeof rawString !== 'string') return {};

            const distribution = {};
            const parts = rawString.split(/[;；]/);

            parts.forEach(part => {
                const item = part.split(/[：:]/);
                if (item.length === 2) {
                    const industry = item[0].trim();
                    const percentage = parseFloat(item[1].replace('%', '').trim());
                    if (industry && !isNaN(percentage)) {
                        distribution[industry] = percentage;
                    }
                }
            });

            if (Object.keys(distribution).length === 0 && rawString.includes('、')) {
                const industries = rawString.split('、');
                const percentage = 100 / industries.length;
                industries.forEach(ind => {
                    distribution[ind.trim()] = percentage;
                });
            }

            return distribution;
        }

        function updateAssetDataTable() {
            const tableBody = document.getElementById('asset-data-table-body');
            if (!tableBody) return;

            tableBody.innerHTML = selectedAssets.map(asset => {
                const latestOccupancy = asset.occupancyHistory[asset.occupancyHistory.length - 1];
                const latestRent = asset.rentHistory[asset.rentHistory.length - 1];

                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 font-mono text-xs text-blue-600">${asset.code}</td>
                        <td class="px-4 py-3">${asset.name}</td>
                        <td class="px-4 py-3">${asset.asset}</td>
                        <td class="px-4 py-3">${asset.type}</td>
                        <td class="px-4 py-3">${asset.location}</td>
                        <td class="px-4 py-3 text-right">${asset.area || 'N/A'}</td>
                        <td class="px-4 py-3 text-right">${latestOccupancy ? latestOccupancy.value.toFixed(1) + '%' : 'N/A'}</td>
                        <td class="px-4 py-3 text-right">${latestRent ? latestRent.value.toFixed(0) : 'N/A'}</td>
                        <td class="px-4 py-3 text-right">${asset.leaseTerm || 'N/A'}</td>
                        <td class="px-4 py-3 text-right">${asset.topTenantsRatio || 'N/A'}</td>
                    </tr>
                `;
            }).join('');
        }

        function updateAssetValuation(selectedIds) {
            const canvas = document.getElementById('asset-valuation-chart');
            if (!canvas) return;
            const table = document.getElementById('asset-valuation-params-table');
            
            const labels = ['2020', '2021', '2022', '2023', '2024'];
            const datasets = [];
            selectedIds.forEach((id, index) => {
                const reitId = id.split('.')[0];
                if (MOCK_DATA.asset_valuation[reitId]) {
                    const color = PALETTE[index % PALETTE.length];
                    const currentVal = MOCK_DATA.asset_valuation[reitId].totalVal;
                    const currentPrice = MOCK_DATA.asset_valuation[reitId].details[0].price;
                    
                    const valHistory = [currentVal * 0.8, currentVal * 0.85, currentVal * 0.9, currentVal * 0.95, currentVal].map(v => v * (1 + (Math.random() - 0.5) * 0.1));
                    const priceHistory = [currentPrice * 0.8, currentPrice * 0.85, currentPrice * 0.9, currentPrice * 0.95, currentPrice].map(v => v * (1 + (Math.random() - 0.5) * 0.1));

                    datasets.push({
                        label: `${MOCK_DATA.reits.find(r=>r.id===id).name} 资产估值(亿)`,
                        data: valHistory,
                        borderColor: color,
                        yAxisID: 'yValuation'
                    });
                     datasets.push({
                        label: `${MOCK_DATA.reits.find(r=>r.id===id).name} 估值单价(元)`,
                        data: priceHistory,
                        borderColor: color,
                        borderDash: [5, 5],
                        yAxisID: 'yPrice'
                    });
                }
            });
            
            if(assetValuationChart) assetValuationChart.destroy();
            assetValuationChart = new Chart(canvas, {
                type: 'line',
                data: { labels, datasets },
                options: { 
                    ...commonChartOptions, 
                    scales: { 
                        yValuation: { type: 'linear', position: 'left', title: { display: true, text: '资产估值(亿元)' } },
                        yPrice: { type: 'linear', position: 'right', title: { display: true, text: '估值单价(元/平方米)' }, grid: { drawOnChartArea: false } }
                    } 
                }
            });

            let tableHTML = `<thead><tr><th>基金简称</th><th>资产名称</th><th>土地剩余年限</th><th>市场租金</th><th>估值折现率</th><th>租金增长率</th></tr></thead><tbody>`;
            selectedIds.forEach(id => {
                const reitId = id.split('.')[0];
                if (MOCK_DATA.asset_valuation[reitId]) {
                    const reitName = MOCK_DATA.reits.find(r => r.id === id).name;
                    MOCK_DATA.asset_valuation[reitId].details.forEach(detail => {
                        tableHTML += `<tr><td>${reitName}</td><td>${detail.name}</td><td>${detail.tenure}</td><td>${detail.rent}</td><td>${detail.discount}</td><td class="text-xs">${detail.growth}</td></tr>`;
                    });
                }
            });
            table.innerHTML = tableHTML + '</tbody>';
        }

        function updateRiskControl(selectedIds) {
            const parsedData = parseCSV(risk_warning_data);

            // Update Chart
            const canvas = document.getElementById('risk-visualization-chart');
            if (!canvas) return;

            const labels = parsedData.map(d => d.证券名称);
            const positionData = parsedData.map(d => parseFloat(d['持仓金额（万元）']));
            const varData = parsedData.map(d => parseFloat(d['VAR值']));

            if (riskVisualizationChart) {
                riskVisualizationChart.destroy();
            }
            riskVisualizationChart = new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: '持仓金额 (万元)',
                            data: positionData,
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            yAxisID: 'y',
                        },
                        {
                            label: 'VAR值',
                            data: varData,
                            type: 'line',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            yAxisID: 'y1',
                            tension: 0.3,
                            fill: true,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '持仓金额 (万元)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'VAR值'
                            },
                            grid: {
                                drawOnChartArea: false, 
                            },
                        },
                    }
                }
            });

            // Update Table
            const table = document.getElementById('risk-warning-table');
            if (!table) return;
            let tableHTML = `<thead><tr><th>代码</th><th>证券名称</th><th>买入均价</th><th>现价</th><th>持仓金额(万)</th><th>持仓集中度</th><th>VAR值</th><th>持仓收益率</th><th>预警线</th><th>止损线</th></tr></thead><tbody>`;
            parsedData.forEach(d => {
                tableHTML += `<tr><td>${d.代码}</td><td>${d.证券名称}</td><td>${d.买入均价}</td><td>${d.现价}</td><td>${d['持仓金额（万元）']}</td><td>${(parseFloat(d.持仓集中度) * 100).toFixed(1)}%</td><td>${d.VAR值}</td><td>${(parseFloat(d.持仓收益率) * 100).toFixed(2)}%</td><td><span class="badge ${d.是否触发预警线 === '是' ? 'yes' : 'no'}">${d.是否触发预警线}</span></td><td><span class="badge ${d.是否触发止损线 === '是' ? 'yes' : 'no'}">${d.是否触发止损线}</span></td></tr>`;
            });
            table.innerHTML = tableHTML + '</tbody>';
        }

        function setupReportSection() {
            const generateBtn = document.getElementById('generate-report-btn');
            const reportOutput = document.getElementById('report-output');

            const mockReports = [
                {
                    title: '2025年第二季度REITs市场回顾与展望',
                    summary: '本报告对2025年第二季度中国公募REITs市场的表现进行了全面回顾，并对未来趋势进行了展望。市场整体呈现稳健增长，但不同板块之间表现分化...',
                    fullText: '<p>市场在第二季度表现出强劲的韧性。基础设施REITs，特别是与新能源、数据中心和高速公路相关的项目，受到了投资者的热捧。其资产的稳定现金流和可预见的增长前景，在当前宏观经济环境下显得尤为珍贵，吸引了大量长期资金的流入。相比之下，受宏观经济周期性影响较大的商业地产和酒店类REITs，则面临一定的租赁市场压力和资产估值调整。我们观察到，投资者对资产质量、运营效率和管理能力的关注度达到了前所未有的高度，优质核心资产的稀缺性愈发凸显。</p><p>展望未来，我们预计市场结构性分化将持续。一方面，国家政策的持续支持和“新基建”战略的深入推进，将继续为相关REITs提供强劲的发展动力。另一方面，随着国内消费市场的逐步复苏，部分位于核心商圈、运营能力卓越的消费基础设施REITs也可能迎来估值修复的窗口期。对于投资者而言，当前的策略应是精选赛道和个券，深入研究底层资产的健康状况，审慎评估租户结构的稳定性与多样性，并高度重视基金管理人的主动管理与运营能力，以此来构建一个能够有效平衡风险与长期收益的投资组合。</p>'
                },
                {
                    title: '仓储物流REITs深度分析：价值洼地还是风险陷阱？',
                    summary: '随着电商的持续发展和供应链的现代化，仓储物流REITs备受关注。本报告深入探讨了该板块的投资价值、增长潜力以及潜在风险...',
                    fullText: '<p>我们通过对国内多个核心经济圈的仓储物流REITs进行深入分析，发现位于核心物流节点、服务于头部电商平台、高端制造业和第三方物流巨头的高标准现代化物流设施，仍然是具有长期投资价值的稀缺核心资产。这些资产通常拥有极高的出租率、稳定的租金增长以及较长的租期，展现出强大的抗周期能力。然而，投资者也需要警惕部分二三线城市或非核心区域可能出现的结构性供应过剩问题，这可能导致未来租金增长乏力和空置率上升的双重风险。</p><p>此外，租户的信用资质和行业集中度也是一个需要仔细甄别的风险点。过度依赖单一或少数几个大租户，尤其当租户所处行业景气度下行时，可能会在租约到期或续租谈判时带来极大的不确定性。因此，我们建议投资者在进行配置时，应优先选择那些资产地理位置优越、路网通达性好、租户结构多元化、且基金管理人具备专业高效运营管理和资本循环能力的仓储物流REITs，以期在分享行业增长红利的同时，有效控制潜在风险，实现长期稳健的投资回报。</p>'
                },
                {
                    title: '新能源REITs投资策略探讨',
                    summary: '在“双碳”目标驱动下，新能源REITs成为市场的新宠。本报告旨在探讨新能源REITs的投资逻辑、估值方法以及长期持有的策略...',
                    fullText: '<p>新能源项目，如风电场和光伏电站，其发电量和上网电价通常有长期购电协议（PPA）保障，因此能够产生高度可预测且稳定的现金流，这与REITs产品追求稳定分红的特性高度契合。在国家“双碳”战略目标的驱动下，这类清洁能源资产也获得了前所未有的政策支持和广阔的市场空间。本报告构建了一个基于现金流折现（DCF）的精细化估值模型，并对几个代表性的新能源REITs进行了深度案例分析，系统探讨了补贴政策变动、电价市场化波动、运营成本控制、设备衰减率以及弃风弃光率等关键变量对项目估值和投资回报的敏感性影响。</p><p>我们的分析表明，尽管新能源REITs前景广阔，但投资者仍需清醒认识其面临的独特挑战，包括关键技术的快速迭代风险、可再生能源补贴的逐步退坡、以及部分地区因电网消纳能力不足而产生的限电风险。在投资决策中，应进行更为审慎和全面的尽职调查，综合考量项目的技术成熟度、地理位置（如光照和风力资源）、运营商的过往业绩和运维经验，以及PPA协议的关键条款（如电价、期限和交易对手信用），以准确评估其长期投资价值。</p>'
                }
            ];

            if (generateBtn) {
                generateBtn.addEventListener('click', () => {
                    if (!reportOutput) return;
                    reportOutput.innerHTML = ''; // Clear previous reports
                    mockReports.forEach(report => {
                        const reportItem = document.createElement('div');
                        reportItem.className = 'report-item';

                        const summaryDiv = document.createElement('div');
                        summaryDiv.className = 'report-summary';
                        summaryDiv.innerHTML = `<h6>${report.title}</h6><p class="text-sm text-gray-600">${report.summary}</p>`;
                        
                        const fullDiv = document.createElement('div');
                        fullDiv.className = 'report-full'; // Initially hidden by CSS
                        fullDiv.innerHTML = report.fullText;

                        reportItem.appendChild(summaryDiv);
                        reportItem.appendChild(fullDiv);
                        reportOutput.appendChild(reportItem);
                    });
                });
            }

            if (reportOutput) {
                reportOutput.addEventListener('click', function(e) {
                    const summary = e.target.closest('.report-summary');
                    if (!summary) return;

                    const fullReport = summary.nextElementSibling;
                    if (fullReport && fullReport.classList.contains('report-full')) {
                        if (fullReport.style.display === 'block') {
                            fullReport.style.display = 'none';
                        } else {
                            fullReport.style.display = 'block';
                        }
                    }
                });
            }
        }

        

        // --- Financial Data Functions ---
        function generateAnalysisText(reitId) {
            const reitInfo = MOCK_DATA.detailed_info[reitId];
            const reitValuation = MOCK_DATA.valuation[reitId.split('.')[0]]; // Valuation data uses reitId without .SZ/.SH
            const reitFinancial = financialReitsData.find(r => r.id === reitId);
            const reitAssets = parsedAssetsData.filter(a => a.code === reitId);
            const reitRisk = MOCK_DATA.risk_warning.find(r => r.code === reitId);

            if (!reitInfo) return '<p>缺少该REIT的详细数据。</p>';

            let analysis = `<h4 class="text-md font-semibold mb-2">${reitInfo['证券简称']} (${reitInfo['代码']})</h4>`;
            analysis += `<p class="text-sm text-gray-700 leading-relaxed">`;

            analysis += `该REIT（${reitInfo['资产类型']}）于${reitInfo['REITs上市日期']}上市，原始权益人为${reitInfo['原始权益人']}。`;

            if (reitFinancial) {
                const quarters = Object.keys(reitFinancial.ebitda).sort();
                const latestQuarter = quarters[quarters.length - 1];
                const ebitdaMargin = (reitFinancial.ebitda[latestQuarter] * 100).toFixed(1);
                const distribution = reitFinancial.distribution[latestQuarter];
                analysis += ` 从最新财务数据（${latestQuarter.replace('Q', '年Q')}）来看，其EBITDA利润率约为<strong>${ebitdaMargin}%</strong>，可供分配金额为<strong>${distribution}亿元</strong>，展现了其盈利能力。`;
            }

            if (reitAssets.length > 0) {
                const avgOccupancy = reitAssets.reduce((sum, asset) => {
                    const latest = asset.occupancyHistory[asset.occupancyHistory.length - 1];
                    return sum + (latest ? latest.value : 0);
                }, 0) / reitAssets.length;
                if (avgOccupancy > 0) {
                    analysis += ` 资产运营层面，平均出租率维持在<strong>${avgOccupancy.toFixed(1)}%</strong>的水平，显示出稳健的运营基础。`;
                }
            }

            if (reitValuation) {
                const pffo = reitValuation.pffo.toFixed(1);
                const navPremium = reitValuation.nav_premium.toFixed(1);
                analysis += ` 估值方面，当前的P/FFO倍数为<strong>${pffo}</strong>，NAV${navPremium >= 0 ? '溢价' : '折价'}率为<strong>${Math.abs(navPremium)}%</strong>。`;
            }

            if (reitRisk) {
                analysis += ` 风险方面，其持仓集中度为<strong>${reitRisk.concentration.toFixed(1)}%</strong>，VaR值为<strong>${reitRisk.var.toFixed(3)}</strong>。`;
            }

            analysis += ` 综合来看，该REIT在运营和财务方面表现稳健，投资者可关注其长期的租金增长潜力和资产升值空间。`;
            analysis += `</p>`;
            return analysis;
        }

        function parseFinancialsCSV(csvText) {
            if (!csvText || csvText.trim() === '') return [];
            const lines = csvText.trim().split(/\r?\n/);
            if (lines.length < 2) return [];

            const headers = lines[0].split(',');
            const data = lines.slice(1).map(line => {
                const values = line.split(',');
                const row = {};
                headers.forEach((header, index) => {
                    row[header.trim()] = values[index] ? values[index].trim() : null;
                });
                return row;
            });

            const groupedData = {};
            data.forEach(row => {
                if (!row.id || !row.quarter) return;
                if (!groupedData[row.id]) {
                    groupedData[row.id] = {
                        id: row.id,
                        name: row.name,
                        ebitda: {},
                        distribution: {},
                        revenueStructure: {},
                        costStructure: {}
                    };
                }
                const quarter = row.quarter.replace('-', 'Q');
                if (row.ebitda) groupedData[row.id].ebitda[quarter] = parseFloat(row.ebitda);
                if (row.distribution) groupedData[row.id].distribution[quarter] = parseFloat(row.distribution);
                if (row.rev_rent) {
                    groupedData[row.id].revenueStructure[quarter] = {
                        rent: parseFloat(row.rev_rent),
                        property: parseFloat(row.rev_prop),
                        parking: parseFloat(row.rev_park),
                        other: parseFloat(row.rev_other)
                    };
                }
                if (row.cost_op) {
                    groupedData[row.id].costStructure[quarter] = {
                        operation: parseFloat(row.cost_op),
                        depreciation: parseFloat(row.cost_dep),
                        tax: parseFloat(row.cost_tax),
                        management: parseFloat(row.cost_mgmt)
                    };
                }
            });

            return Object.values(groupedData);
        }

        let financialReitsData = [];

        

        const financialColors = ['#5470C6', '#91CC75', '#EE6666', '#FAC858', '#73C0DE', '#3BA272'];

        function updateEbitdaChart(selectedIds) {
            try {
                if (!ebitdaChart) return;
                if (selectedIds.length === 0 || !financialReitsData || financialReitsData.length === 0) {
                    ebitdaChart.clear();
                    return;
                }

                const allQuarters = [...new Set(financialReitsData.flatMap(r => Object.keys(r.ebitda || {})))].sort();
                if (allQuarters.length === 0) {
                    ebitdaChart.clear();
                    return;
                }

                const xAxisData = allQuarters.map(q => q.replace('Q', ' Q'));

                const relevantData = selectedIds.map(id => {
                    return financialReitsData.find(reit => reit.id === id) || { id: id, name: MOCK_DATA.reits.find(r => r.id === id)?.name || id, ebitda: {} };
                });

                const series = relevantData.map((reit, idx) => ({
                    name: reit.name,
                    type: 'line',
                    smooth: true,
                    data: allQuarters.map(q => reit.ebitda[q] || null),
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: { color: financialColors[idx % financialColors.length] },
                    lineStyle: { width: 3 }
                }));

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: params => {
                            let html = `${params[0].axisValue}<br>`;
                            params.forEach(param => {
                                if (param.value !== null && param.value !== undefined) {
                                    html += `${param.marker} ${param.seriesName}: ${(param.value * 100).toFixed(1)}%<br>`;
                                }
                            });
                            return html;
                        }
                    },
                    legend: { data: relevantData.map(r => r.name), top: 10, type: 'scroll' },
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: xAxisData
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { formatter: p => (p * 100).toFixed(0) + '%' }
                    },
                    series: series
                };
                ebitdaChart.hideLoading();
                ebitdaChart.setOption(option, true);
                ebitdaChart.resize();
            } catch (e) {
                console.error("Error in updateEbitdaChart:", e);
                if (ebitdaChart) ebitdaChart.showLoading('default', { text: '图表加载失败', color: '#c23531' });
            }
        }

        function updateDistributionChart(selectedIds) {
            try {
                if (!distributionChart || selectedIds.length === 0 || financialReitsData.length === 0) return;

                const allQuarters = [...new Set(financialReitsData.flatMap(r => Object.keys(r.distribution || {})))].sort();
                if (allQuarters.length === 0) {
                    distributionChart.clear();
                    return;
                }

                const xAxisData = allQuarters.map(q => q.replace('Q', ' Q'));

                const relevantData = selectedIds.map(id => {
                    return financialReitsData.find(reit => reit.id === id) || { id: id, name: MOCK_DATA.reits.find(r => r.id === id)?.name || id, distribution: {} };
                });

                const series = relevantData.map((reit, idx) => ({
                    name: reit.name,
                    type: 'bar',
                    data: allQuarters.map(q => reit.distribution[q] || null),
                    itemStyle: {
                        color: financialColors[idx % financialColors.length],
                        borderRadius: [5, 5, 0, 0]
                    },
                    barGap: '20%',
                    barWidth: '25%'
                }));

                const option = {
                    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                    legend: { data: relevantData.map(r => r.name), top: 10, type: 'scroll' },
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: { type: 'category', data: xAxisData },
                    yAxis: { type: 'value', name: '金额 (亿元)' },
                    series: series
                };
                distributionChart.hideLoading();
                distributionChart.setOption(option, true);
            } catch (e) {
                console.error("Error in updateDistributionChart:", e);
                if (distributionChart) distributionChart.showLoading('default', { text: '分配图加载失败: ' + e.message, color: '#c23531' });
            }
        }

        function updateGrowthChart(selectedIds) {
            try {
                if (!growthChart || selectedIds.length === 0 || financialReitsData.length === 0) return;

                const allQuarters = [...new Set(financialReitsData.flatMap(r => Object.keys(r.distribution || {})))].sort();
                if (allQuarters.length === 0) {
                    growthChart.clear();
                    return;
                }

                const xAxisData = allQuarters.map(q => q.replace('Q', ' Q'));

                const relevantData = selectedIds.map(id => {
                    return financialReitsData.find(reit => reit.id === id) || { id: id, name: MOCK_DATA.reits.find(r => r.id === id)?.name || id, distribution: {} };
                });

                const amountSeries = relevantData.map((reit, idx) => ({
                    name: reit.name + '可供分配金额',
                    type: 'bar',
                    data: allQuarters.map(q => reit.distribution[q] || null),
                    itemStyle: { color: financialColors[idx % financialColors.length] },
                    barWidth: '25%'
                }));

                const growthSeries = relevantData.map((reit, idx) => {
                    const values = [];
                    const distValues = allQuarters.map(q => reit.distribution[q] || null);
                    for (let i = 1; i < distValues.length; i++) {
                        const current = distValues[i];
                        const previous = distValues[i - 1];
                        if (current !== null && previous !== null && previous !== 0) {
                            const growth = (current - previous) / previous;
                            values.push(growth);
                        } else {
                            values.push(null);
                        }
                    }
                    return {
                        name: reit.name + '环比增长',
                        type: 'line',
                        smooth: true,
                        data: [null, ...values],
                        yAxisIndex: 1,
                        symbol: 'circle',
                        symbolSize: 8,
                        itemStyle: { color: financialColors[idx % financialColors.length] },
                        lineStyle: { width: 3, type: 'dashed' }
                    }
                });

                const option = {
                    tooltip: {
                        trigger: 'axis',
                        formatter: params => {
                            let html = `${params[0].axisValue}<br>`;
                            params.forEach(p => {
                                let value = p.value;
                                if (p.value !== null && p.value !== undefined) {
                                    if (p.seriesName.includes('环比增长')) {
                                        value = (value * 100).toFixed(2) + '%';
                                    } else {
                                        value = parseFloat(value).toFixed(2) + ' 亿元';
                                    }
                                    html += `${p.marker} ${p.seriesName}: ${value}<br>`;
                                }
                            });
                            return html;
                        }
                    },
                    legend: {
                        data: [...relevantData.map(r => r.name + '可供分配金额'), ...relevantData.map(r => r.name + '环比增长')],
                        top: 10,
                        type: 'scroll'
                    },
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: { type: 'category', data: xAxisData },
                    yAxis: [
                        { type: 'value', name: '金额 (亿元)' },
                        { type: 'value', name: '增长率', axisLabel: { formatter: p => (p * 100).toFixed(0) + '%' } }
                    ],
                    series: [...amountSeries, ...growthSeries]
                };
                growthChart.hideLoading();
                growthChart.setOption(option, true);
            } catch (e) {
                console.error("Error in updateGrowthChart:", e);
                if (growthChart) growthChart.showLoading('default', { text: '增长图加载失败: ' + e.message, color: '#c23531' });
            }
        }
        

        function updateStructureChart(selectedIds) {
            try {
                if (!structureChart || selectedIds.length === 0 || financialReitsData.length === 0) return;

                const allQuarters = [...new Set(financialReitsData.flatMap(r => Object.keys(r.revenueStructure || {})))].sort();
                if (allQuarters.length === 0) {
                    structureChart.clear();
                    return;
                }
                const latestQuarter = allQuarters[allQuarters.length - 1];

                const categories = ['租金收入', '物业收入', '停车费收入', '其他收入'];
                const reitNames = selectedIds.map(id => (financialReitsData.find(r => r.id === id) || {}).name || id);

                const series = categories.map(category => {
                    return {
                        name: category,
                        type: 'bar',
                        stack: 'total',
                        label: { show: true, formatter: (p) => (p.value * 100).toFixed(1) + '%' },
                        emphasis: { focus: 'series' },
                        data: selectedIds.map(id => {
                            const reit = financialReitsData.find(r => r.id === id);
                            if (!reit || !reit.revenueStructure[latestQuarter]) return 0;
                            const structure = reit.revenueStructure[latestQuarter];
                            switch (category) {
                                case '租金收入': return structure.rent;
                                case '物业收入': return structure.property;
                                case '停车费收入': return structure.parking;
                                case '其他收入': return structure.other;
                                default: return 0;
                            }
                        })
                    };
                });

                const option = {
                    title: { text: latestQuarter.replace('Q', ' Q') + ' 收入结构', left: 'center', top: 10 },
                    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, formatter: params => {
                        let res = params[0].name + '<br/>';
                        params.forEach(p => {
                            res += `${p.marker}${p.seriesName}: ${(p.value * 100).toFixed(1)}%<br/>`;
                        });
                        return res;
                    }},
                    legend: { data: categories, top: 30, type: 'scroll' },
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: { type: 'value', axisLabel: { formatter: p => (p * 100).toFixed(0) + '%' } },
                    yAxis: { type: 'category', data: reitNames },
                    series: series,
                    color: financialColors
                };
                structureChart.hideLoading();
                structureChart.setOption(option, true);
            } catch (e) {
                console.error("Error in updateStructureChart:", e);
                if (structureChart) structureChart.showLoading('default', { text: '收入结构图加载失败: ' + e.message, color: '#c23531' });
            }
        }

        function updateCostChart(selectedIds) {
            try {
                if (!costChart || selectedIds.length === 0 || financialReitsData.length === 0) return;

                const allQuarters = [...new Set(financialReitsData.flatMap(r => Object.keys(r.costStructure || {})))].sort();
                if (allQuarters.length === 0) {
                    costChart.clear();
                    return;
                }
                const latestQuarter = allQuarters[allQuarters.length - 1];

                const categories = ['营业成本', '折旧摊销', '税金及附加', '管理费用'];
                const reitNames = selectedIds.map(id => (financialReitsData.find(r => r.id === id) || {}).name || id);

                const series = categories.map(category => {
                    return {
                        name: category,
                        type: 'bar',
                        stack: 'total',
                        label: { show: true, formatter: (p) => (p.value * 100).toFixed(1) + '%' },
                        emphasis: { focus: 'series' },
                        data: selectedIds.map(id => {
                            const reit = financialReitsData.find(r => r.id === id);
                            if (!reit || !reit.costStructure[latestQuarter]) return 0;
                            const structure = reit.costStructure[latestQuarter];
                            switch (category) {
                                case '营业成本': return structure.operation;
                                case '折旧摊销': return structure.depreciation;
                                case '税金及附加': return structure.tax;
                                case '管理费用': return structure.management;
                                default: return 0;
                            }
                        })
                    };
                });

                const option = {
                    title: { text: latestQuarter.replace('Q', ' Q') + ' 成本结构', left: 'center', top: 10 },
                    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                    legend: { data: categories, top: 30, type: 'scroll' },
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: { type: 'value', axisLabel: { formatter: p => (p * 100).toFixed(0) + '%' } },
                    yAxis: { type: 'category', data: reitNames },
                    series: series,
                    color: financialColors.slice().reverse()
                };
                costChart.hideLoading();
                costChart.setOption(option, true);
            } catch (e) {
                console.error("Error in updateCostChart:", e);
                if (costChart) costChart.showLoading('default', { text: '成本结构图加载失败: ' + e.message, color: '#c23531' });
            }
        }

        // --- Asset Operations Functions ---
        // 原始资产运营数据来自资产运营.html
        const assetsCsvData = `基金代码,基金简称,资产名称,类型,资产所在地,产业园区,期末出租率(%),,,,期内租金单价水平（元/平方米/月）,,,,可租赁面积(万平方米), 租户集中度 （前五大占比）,剩余租期,行业分布
,,,,,,2024,2023,2022,2021,2024,2023,2022,2021,2024,2024,2024,2024
180101.OF,博时招商蛇口产业园REIT,万海大厦,办公,深圳市南山区,蛇口网谷产业园,93.66,91,81,88,124.68,130.54,140.74,-,5.26,42.5,1.48,新一代信息技术、现代服务业、商业、数字文创
180101.OF,博时招商蛇口产业园REIT,万融大厦,办公,深圳市南山区,蛇口网谷产业园,88.96,92,84,88,114.81,120.46,119.01,-,4.14,38.2,1.03,
180101.OF,博时招商蛇口产业园REIT,招商局光明科技园科技企业加速器二期,办公+厂房,深圳市光明区,招商局光明科技园,90.5,97,97.1,98.1,47.8,47.9,46.2,-,18.7,46.2,2.8,
508000.OF,华安张江光大园REIT,张江光大园,办公,上海市浦东新区,张江科学城,91.37,90.23,96.88,96.88,164.4,163.8,158.4,-,7.02,55.8,2.8,集成电路：45%；生物医药：30%；人工智能：15%；其他：10%
508021.OF,国泰君安临港创新智造产业园REIT,临港奉贤智造园一期,厂房,上海市奉贤区,临港奉贤园区,94.26,100,100,100,38.4,36,36,-,13.6,72.3,4,高端制造: 60%; 新能源: 25%; 物流仓储: 10%; 其他: 5%
508088.OF,国泰君安东久新经济REIT,东久（金山）智造园,厂房,上海市金山区,金山工业区,100,100,100,100,28.27,27.6,27.6,-,10.2,85.6,5.3,汽车制造: 70%; 电子元件: 20%; 其他: 10%
508056.OF,中金普洛斯仓储物流REIT,普洛斯广州保税物流园,仓储,广州市黄埔区,广州保税区,100,100,100,100,53.8,53.8,51.8,-,14.7,100,2.1,
508001.OF,红土盐田港仓储物流REIT,盐田港现代物流中心,仓储,深圳市盐田区,盐田港,100,100,100,100,50.7,50.7,50.7,-,17.4,100,3.3,
508006.OF,中航首钢绿能REIT,首钢生物质能源项目,能源,北京市门头沟区,,-,98.09,95.93,80.9,39,36.6,35.4,33.9,5.12,22.29,1.92,
508097.OF,华泰紫金南京建邺产业园REIT,国际研发总部园,办公,南京市建邺区,国际研发总部园,82.29,82.31,84.93,88.12,68.39,69.3,58.8,-,27.08,55.1,3.5,科技研发：37.36%；法律服务：20.50%；专业服务：14.50%；金融服务：13.69%
508010.OF,中金重庆两江产业园REIT,凤凰座项目,办公,重庆市渝北区,两江数字经济产业园,82.27,82.46,75.58,71.5,44,-,-,-,79.57,48.2,2.1,信息技术：44.95%；专业服务：41.92%
508010.OF,中金重庆两江产业园REIT,双鱼座项目,办公,重庆市渝北区,两江数字经济产业园,98.69,95.86,88,80.74,62,-,-,-,79.57,61.3,2.3,
508010.OF,中金重庆两江产业园REIT,双子座项目,办公,重庆市渝北区,两江数字经济产业园,92.51,91.2,88.1,84.5,58,-,-,-,79.57,55.4,2.5,`;

        function parseAssetsCSV(data) {
            const rows = data.trim().split('\n').slice(2); // Skip header rows
            const assets = rows.map(row => {
                const cols = row.split(',');

                // 转换基金代码格式从.OF到.SZ/.SH以匹配主数据
                let fundCode = cols[0] || '';
                if (fundCode.includes('.OF')) {
                    // 简单的映射规则
                    if (fundCode.startsWith('180')) {
                        fundCode = fundCode.replace('.OF', '.SZ');
                    } else if (fundCode.startsWith('508')) {
                        fundCode = fundCode.replace('.OF', '.SH');
                    }
                }

                // 更安全的数据解析
                const occupancyHistory = [
                    { year: '2021', value: parseFloat(cols[9]) || null },
                    { year: '2022', value: parseFloat(cols[8]) || null },
                    { year: '2023', value: parseFloat(cols[7]) || null },
                    { year: '2024', value: parseFloat(cols[6]) || null },
                ].filter(d => d.value !== null && !isNaN(d.value));

                const rentHistory = [
                    { year: '2021', value: parseFloat(cols[13]) || null },
                    { year: '2022', value: parseFloat(cols[12]) || null },
                    { year: '2023', value: parseFloat(cols[11]) || null },
                    { year: '2024', value: parseFloat(cols[10]) || null },
                ].filter(d => d.value !== null && !isNaN(d.value));

                // 如果没有历史数据，生成模拟数据
                if (occupancyHistory.length === 0) {
                    occupancyHistory.push(
                        { year: '2022', value: 85 + Math.random() * 10 },
                        { year: '2023', value: 87 + Math.random() * 8 },
                        { year: '2024', value: 89 + Math.random() * 6 }
                    );
                }

                if (rentHistory.length === 0) {
                    rentHistory.push(
                        { year: '2022', value: 80 + Math.random() * 40 },
                        { year: '2023', value: 85 + Math.random() * 45 },
                        { year: '2024', value: 90 + Math.random() * 50 }
                    );
                }

                return {
                    code: fundCode,
                    name: cols[1] || '',
                    asset: cols[2] || '',
                    type: cols[3] || '',
                    location: cols[4] || '',
                    park: cols[5] || '',
                    occupancyHistory,
                    rentHistory,
                    area: parseFloat(cols[14]) || (5 + Math.random() * 20),
                    topTenantsRatio: parseFloat(cols[15]) || (30 + Math.random() * 40),
                    leaseTerm: parseFloat(cols[16]) || (2 + Math.random() * 3),
                    industryDistributionRaw: cols[17] || '科技：40%；金融：30%；其他：30%'
                };
            }).filter(asset => asset.code && asset.name && asset.asset); // 过滤掉空数据

            console.log('Parsed assets:', assets.length, 'items');
            console.log('Sample asset codes:', assets.slice(0, 3).map(a => a.code));
            return assets;
        }

        // --- Gemini Implemented Features ---

        function generateRiskData() {
            MOCK_DATA.risk_warning = []; // Clear existing data before populating
            MOCK_DATA.reits.forEach(reit => {
                // Generate random values for the new properties
                const buyPrice = 2 + Math.random() * 3; // Random price between 2 and 5
                const currentPrice = buyPrice * (1 + (Math.random() - 0.5) * 0.1); // +/- 5% from buyPrice
                const positionValue = 50 + Math.random() * 150; // Random value between 50 and 200
                const concentration = 5 + Math.random() * 15; // Random concentration between 5% and 20%
                const varValue = 0.01 + Math.random() * 0.03; // Random VaR between 1% and 4%

                MOCK_DATA.risk_warning.push({
                    code: reit.id,
                    name: reit.name,
                    price_warning: Math.random() > 0.8 ? '预警' : '正常',
                    concentration_risk: Math.random() * 25, // percentage
                    valuation_risk: Math.random() * 0.12, // deviation
                    liquidity_risk: Math.random() * 5, // score 1-5
                    // Add the new properties
                    buyPrice: parseFloat(buyPrice.toFixed(3)),
                    currentPrice: parseFloat(currentPrice.toFixed(3)),
                    positionValue: parseFloat(positionValue.toFixed(0)),
                    concentration: parseFloat(concentration.toFixed(2)),
                    var: parseFloat(varValue.toFixed(3))
                });
            });
        }

        

        

        function updateRiskChart(selectedIds) {
            const canvas = document.getElementById('risk-visualization-chart');
            if (!canvas) return;
            if (riskVisualizationChart) riskVisualizationChart.destroy();

            const riskData = MOCK_DATA.risk_warning.filter(r => selectedIds.includes(r.code));
            if (riskData.length === 0) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = "16px 'Noto Sans SC'";
                ctx.fillStyle = '#6b7280';
                ctx.textAlign = 'center';
                ctx.fillText('请选择标的以显示风险可视化', canvas.width / 2, canvas.height / 2);
                return;
            }

            const warningRatio = parseFloat(document.getElementById('warning-line-ratio').value) || 0.9;
            const stopLossRatio = parseFloat(document.getElementById('stop-loss-line-ratio').value) || 0.8;
            const labels = MOCK_DATA.daily[selectedIds[0].split('.')[0]]?.labels || [];
            const datasets = [];

            selectedIds.forEach((id, index) => {
                const reitId = id.split('.')[0];
                const dailyData = MOCK_DATA.daily[reitId];
                const riskInfo = riskData.find(r => r.code === id);

                if (!dailyData || !riskInfo) return;

                const color = PALETTE[index % PALETTE.length];
                const warningPrice = riskInfo.buyPrice * warningRatio;
                const stopLossPrice = riskInfo.buyPrice * stopLossRatio;

                datasets.push({
                    label: `${riskInfo.name} 净值`,
                    data: dailyData.prices,
                    borderColor: color,
                    borderWidth: 2,
                    tension: 0.1,
                    pointRadius: 0
                });

                datasets.push({
                    label: `${riskInfo.name} 预警线`,
                    data: Array(labels.length).fill(warningPrice),
                    borderColor: '#fbbc05',
                    borderWidth: 1.5,
                    borderDash: [5, 5],
                    pointRadius: 0
                });

                datasets.push({
                    label: `${riskInfo.name} 止损线`,
                    data: Array(labels.length).fill(stopLossPrice),
                    borderColor: '#ea4335',
                    borderWidth: 1.5,
                    borderDash: [5, 5],
                    pointRadius: 0
                });
            });

            riskVisualizationChart = new Chart(canvas, {
                type: 'line',
                data: { labels, datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'top' } },
                    scales: {
                        x: { title: { display: true, text: '日期' } },
                        y: { title: { display: true, text: '净值 (元)' } }
                    }
                }
            });
        }

        // --- Main Application Logic ---
        document.addEventListener('DOMContentLoaded', () => {
            // ... (existing setup code) ...

            const updateSection = (sectionId) => {
                const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                if (!selectedIds) return;

                switch (sectionId) {
                    case 'section-basic-info':
                        safeExecute(() => renderComparisonGrid(selectedIds), 'Comparison grid update');
                        break;
                    case 'section-asset-valuation':
                        safeExecute(() => updateAssetValuation(selectedIds), 'Asset valuation update');
                        break;
                    case 'section-asset-ops':
                        safeExecute(() => updateAssetOperations(selectedIds), 'Asset operations update');
                        break;
                    case 'section-financials':
                        safeExecute(() => updateFinancials(selectedIds), 'Financial data update');
                        break;
                    case 'section-trading':
                        safeExecute(() => updatePerformanceTable(selectedIds), 'Performance table update');
                        if (MOCK_DATA.daily[selectedIds[0]?.split('.')[0]]) {
                            safeExecute(() => updatePriceTurnoverChart(selectedIds, priceSlider.getRange()), 'Price turnover chart update');
                        }
                        safeExecute(() => updateBubbleChart(selectedIds), 'Bubble chart update');
                        safeExecute(() => updateRadarChart(selectedIds), 'Radar chart update');
                        break;
                    case 'section-risk':
                        safeExecute(() => updateRiskChart(selectedIds), 'Risk chart update');
                        safeExecute(() => updateRiskWarningTable(), 'Risk warning table update');
                        break;
                    case 'section-reports':
                    case 'section-backtest':
                        break;
                }
            }

            // ... (existing setup code) ...

            // --- Restore Report Section Controls & Logic ---
            const reportMetricsButton = document.getElementById('report-metrics-button');
            const reportMetricsDropdown = document.getElementById('report-metrics-dropdown');
            if (reportMetricsButton && reportMetricsDropdown) {
                const reportMetricsGroups = {
                    '基础数据': { listingDate: '上市日期', originalHolder: '原始权益人' },
                    '资产运营': { income: '总收入', ebit: 'EBITDA' },
                    '资产估值': { nav_premium: 'NAV溢价率', pffo: 'P/FFO' },
                    '财务数据': { dist_ratio: '派息率' }
                };
                Object.keys(reportMetricsGroups).forEach(groupName => {
                    const groupDiv = document.createElement('div');
                    groupDiv.innerHTML = `<h4 class="font-semibold text-sm">${groupName}</h4>`;
                    Object.keys(reportMetricsGroups[groupName]).forEach(metricKey => {
                        groupDiv.innerHTML += `<label class="font-normal block"><input type="checkbox" value="${metricKey}" checked> ${reportMetricsGroups[groupName][metricKey]}</label>`;
                    });
                    reportMetricsDropdown.appendChild(groupDiv);
                });
                reportMetricsButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    reportMetricsDropdown.classList.toggle('hidden');
                });
                document.addEventListener('click', (e) => {
                    if (!reportMetricsButton.contains(e.target) && !reportMetricsDropdown.contains(e.target)) {
                        reportMetricsDropdown.classList.add('hidden');
                    }
                });
            }

            // --- Restore Risk Section Controls & Logic ---
            const riskMetricsContainer = document.getElementById('risk-metrics-selector');
            if (riskMetricsContainer) {
                const riskMetrics = { concentration: '集中度', var: 'VaR' };
                riskMetricsContainer.innerHTML = ''; // Clear first
                Object.keys(riskMetrics).forEach(key => {
                    riskMetricsContainer.innerHTML += `<label class="inline-flex items-center"><input type="checkbox" value="${key}" checked> <span class="ml-2">${riskMetrics[key]}</span></label>`;
                });
                riskMetricsContainer.addEventListener('change', updateRiskWarningTable);
            }
            const riskModal = document.getElementById('risk-modal');
            const riskSettingsBtn = document.getElementById('risk-settings-btn');
            if(riskModal && riskSettingsBtn) {
                riskSettingsBtn.addEventListener('click', () => riskModal.style.display = 'flex');
                document.getElementById('cancel-risk-settings').addEventListener('click', () => riskModal.style.display = 'none');
                document.getElementById('save-risk-settings').addEventListener('click', () => {
                    updateRiskWarningTable();
                    updateRiskChart(getSelectedReits()); // Also update chart
                    riskModal.style.display = 'none';
                });
            }

            // ... (the rest of the DOMContentLoaded listener) ...

        function setupReportGenerator() {
            const mockReports = [
                {
                    title: '2025年第二季度REITs市场回顾与展望',
                    summary: '本报告对2025年第二季度中国公募REITs市场的表现进行了全面回顾，并对未来趋势进行了展望。市场整体呈现稳健增长，但不同板块之间表现分化...',
                    fullText: '<p>详细分析...</p><p>市场在第二季度表现出强劲的韧性。基础设施REITs，特别是与新能源和数据中心相关的项目，受到了投资者的热捧。相比之下，商业地产和酒店类REITs则面临一定的压力。</p><p>展望未来，我们预计...</p>'
                },
                {
                    title: '仓储物流REITs深度分析：价值洼地还是风险陷阱？',
                    summary: '随着电商的持续发展和供应链的现代化，仓储物流REITs备受关注。本报告深入探讨了该板块的投资价值、增长潜力以及潜在风险...',
                    fullText: '<p>详细分析...</p><p>我们通过对多个仓储物流REITs的资产质量、租户结构和地理位置进行分析，发现高质量的现代化物流设施仍然是稀缺资源。然而，投资者也需要警惕部分区域可能出现的供应过剩问题。</p>'
                },
                {
                    title: '新能源REITs投资策略探讨',
                    summary: '在“双碳”目标驱动下，新能源REITs成为市场的新宠。本报告旨在探讨新能源REITs的投资逻辑、估值方法以及长期持有的策略...',
                    fullText: '<p>详细分析...</p><p>新能源项目，如风电场和光伏电站，具有稳定的现金流和长期的政策支持。本报告构建了一个基于现金流折现（DCF）的估值模型，并对几个代表性的新能源REITs进行了案例分析。</p>'
                }
            ];

            document.getElementById('generate-report-btn').addEventListener('click', () => {
                const reportOutput = document.getElementById('report-output');
                reportOutput.innerHTML = ''; // Clear previous reports

                mockReports.forEach(report => {
                    const reportItem = document.createElement('div');
                    reportItem.className = 'report-item';

                    const summaryDiv = document.createElement('div');
                    summaryDiv.className = 'report-summary';
                    summaryDiv.innerHTML = `<h6>${report.title}</h6><p class="text-sm text-gray-600">${report.summary}</p>`;
                    
                    const fullDiv = document.createElement('div');
                    fullDiv.className = 'report-full';
                    fullDiv.innerHTML = report.fullText;

                    reportItem.appendChild(summaryDiv);
                    reportItem.appendChild(fullDiv);
                    reportOutput.appendChild(reportItem);

                    // The click listener for expanding/collapsing will be handled by the delegated listener on report-output
                });
            });

            document.getElementById('report-output').addEventListener('click', function(e) {
                const summary = e.target.closest('.report-summary');
                if (!summary) return;

                const fullReport = summary.nextElementSibling;
                const isHidden = fullReport.classList.contains('hidden');

                fullReport.classList.toggle('hidden');

                // No dynamic generation here, just toggle visibility of static content
            });
        }

        // This will run after the main script has initialized everything.
        window.addEventListener('DOMContentLoaded', () => {
            // A short delay to ensure the main script's DOMContentLoaded has finished.
            setTimeout(() => {
                generateRiskData();

                // Add a listener to trigger the risk visualization update
                const selectorPanel = document.getElementById('global-selector-panel');
                if(selectorPanel) {
                    // This is a robust way to add functionality without overriding existing listeners
                    selectorPanel.addEventListener('change', () => {
                        const selectedIds = getSelectedReits();
                        updateRiskVisualization(selectedIds);
                    });
                }

                // Also trigger an initial update in case there's a default selection
                const selectedIds = getSelectedReits();
                updateRiskVisualization(selectedIds);

                // Update visualization when switching to the risk tab
                const mainNav = document.getElementById('main-nav');
                if (mainNav) {
                    mainNav.addEventListener('click', (e) => {
                        if (e.target.closest('a')?.dataset.section === 'section-risk') {
                             const selectedIds = getSelectedReits();
                             updateRiskVisualization(selectedIds);
                        }
                    });
                }
            }, 100);
        });

        

        function initAssetsPage() {
            assetsData = parseAssetsCSV(assetsCsvData);
            populateFundDropdown();
            fillTableData();
        }

        function populateFundDropdown() {
            const fundSelect = document.getElementById('fundSelect');
            if (!fundSelect) return;

            const funds = [...new Map(assetsData.map(item => [item.code, item])).values()];

            funds.forEach(fund => {
                const option = document.createElement('option');
                option.value = fund.code;
                option.textContent = `${fund.code} ${fund.name}`;
                fundSelect.appendChild(option);
            });
        }

        function updateAssetDropdown() {
            const fundSelect = document.getElementById('fundSelect');
            const assetSelect = document.getElementById('assetSelect');
            const mainContent = document.getElementById('mainContent');
            const initialMessage = document.getElementById('initialMessage');

            console.log('updateAssetDropdown called, fundSelect value:', fundSelect?.value);
            console.log('assetsData length:', assetsData.length);

            if (!fundSelect || !assetSelect) {
                console.error('Fund or asset select elements not found');
                return;
            }

            assetSelect.innerHTML = '<option value="">请选择资产</option>';
            if (mainContent) mainContent.classList.add('hidden');
            if (initialMessage) initialMessage.classList.remove('hidden');

            if (!fundSelect.value) {
                assetSelect.disabled = true;
                assetSelect.classList.add('bg-gray-50', 'cursor-not-allowed');
                console.log('No fund selected, disabling asset select');
            } else {
                assetSelect.disabled = false;
                assetSelect.classList.remove('bg-gray-50', 'cursor-not-allowed');
                const selectedFundAssets = assetsData.filter(item => item.code === fundSelect.value);
                console.log('Found assets for fund:', selectedFundAssets.length);

                selectedFundAssets.forEach(asset => {
                    const option = document.createElement('option');
                    option.value = asset.asset;
                    option.textContent = asset.asset;
                    assetSelect.appendChild(option);
                    console.log('Added asset option:', asset.asset);
                });
            }
            fillTableData(); // Update table when fund selection changes
        }

        function displayAssetData() {
            const assetSelect = document.getElementById('assetSelect');
            const mainContent = document.getElementById('mainContent');
            const initialMessage = document.getElementById('initialMessage');

            if (!assetSelect) return;

            const assetName = assetSelect.value;
            if (!assetName) {
                 if (mainContent) mainContent.classList.add('hidden');
                 if (initialMessage) initialMessage.classList.remove('hidden');
                 return;
            }

            if (mainContent) mainContent.classList.remove('hidden');
            if (initialMessage) initialMessage.classList.add('hidden');

            const asset = assetsData.find(a => a.asset === assetName);
            if (!asset) return;

            updateAssetInfo(asset);
            updateOccupancyChart(asset);
            updateRentChart(asset);
            updateConcentrationChart(asset);
        }

        function updateAssetInfo(asset) {
            const assetInfo = document.getElementById('assetInfo');
            if (!assetInfo) return;

            const industryDist = parseIndustryDistribution(asset.industryDistributionRaw);
            let industryHtml = Object.keys(industryDist).length > 0
                ? '<ul class="list-disc list-inside mt-2 text-sm text-gray-600">' + Object.entries(industryDist).map(([ind, pct]) => `<li>${ind}: ${pct.toFixed(2)}%</li>`).join('') + '</ul>'
                : '<p class="text-sm text-gray-500 mt-2">无详细行业分布数据</p>';

            assetInfo.innerHTML = `
                <h3 class="text-xl font-bold text-gray-900">${asset.asset}</h3>
                <p class="text-sm text-gray-500 mb-4">${asset.name} (${asset.code})</p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                        <p class="text-xs text-gray-500">类型</p>
                        <p class="font-semibold">${asset.type}</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">位置</p>
                        <p class="font-semibold">${asset.location}</p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">前五大租户占比</p>
                        <p class="font-semibold">${asset.topTenantsRatio ? asset.topTenantsRatio + '%' : 'N/A'}</p>
                    </div>
                     <div>
                        <p class="text-xs text-gray-500">产业园区</p>
                        <p class="font-semibold">${asset.park || 'N/A'}</p>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t">
                    <h4 class="font-semibold text-gray-700">行业分布</h4>
                    ${industryHtml}
                </div>
            `;
        }

        function updateOccupancyChart(asset) {
            const ctx = document.getElementById('occupancyChart');
            if (!ctx) return;

            if (occupancyChart) occupancyChart.destroy();

            occupancyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: asset.occupancyHistory.map(d => d.year),
                    datasets: [{
                        label: '期末出租率 (%)',
                        data: asset.occupancyHistory.map(d => d.value),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: { display: true, text: `${asset.asset} - 出租率趋势`, font: { size: 16 } },
                        legend: { display: false }
                    },
                    scales: { y: { beginAtZero: false, suggestedMin: 70, suggestedMax: 100, title: { display: true, text: '出租率 (%)' } } }
                }
            });
        }

        function updateRentChart(asset) {
            const ctx = document.getElementById('rentChart');
            if (!ctx) return;

            if (rentChart) rentChart.destroy();

            rentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: asset.rentHistory.map(d => d.year),
                    datasets: [{
                        label: '租金单价 (元/㎡/月)',
                        data: asset.rentHistory.map(d => d.value),
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: { display: true, text: `${asset.asset} - 租金单价趋势`, font: { size: 16 } },
                        legend: { display: false }
                    },
                    scales: { y: { beginAtZero: false, title: { display: true, text: '租金 (元/㎡/月)' } } }
                }
            });
        }

        function updateConcentrationChart(asset) {
            const ctx = document.getElementById('concentrationChart');
            if (!ctx) return;

            if (concentrationChart) concentrationChart.destroy();

            const industryDist = parseIndustryDistribution(asset.industryDistributionRaw);
            const labels = Object.keys(industryDist);
            const data = Object.values(industryDist);

            const hasData = labels.length > 0;

            concentrationChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: hasData ? labels : ['无数据'],
                    datasets: [{
                        data: hasData ? data : [100],
                        backgroundColor: hasData ? [
                            'rgba(59, 130, 246, 0.8)', 'rgba(239, 68, 68, 0.8)', 'rgba(245, 158, 11, 0.8)',
                            'rgba(16, 185, 129, 0.8)', 'rgba(139, 92, 246, 0.8)', 'rgba(236, 72, 153, 0.8)'
                        ] : ['#E5E7EB'],
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: { display: true, text: `${asset.asset} - 行业分布`, font: { size: 16 } },
                        legend: { position: 'right', display: hasData },
                        tooltip: {
                            enabled: hasData,
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += context.parsed.toFixed(2) + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }

        function fillTableData() {
            const tableBody = document.getElementById('tableBody');
            const tableContainer = document.getElementById('dataTableContainer');
            const fundSelect = document.getElementById('fundSelect');

            if (!tableBody || !tableContainer || !fundSelect) return;

            const fundCode = fundSelect.value;

            tableBody.innerHTML = ''; // Clear existing rows

            if (!fundCode) {
                tableContainer.classList.add('hidden'); // Hide table if no fund is selected
                return;
            }

            tableContainer.classList.remove('hidden'); // Show table
            const dataToDisplay = assetsData.filter(asset => asset.code === fundCode);

            dataToDisplay.forEach(asset => {
                const row = document.createElement('tr');
                row.className = 'bg-white border-b hover:bg-gray-50';

                row.innerHTML = `
                    <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap">${asset.name}</td>
                    <td class="px-4 py-3">${asset.asset}</td>
                    <td class="px-4 py-3">${asset.type}</td>
                    <td class="px-4 py-3">${asset.location}</td>
                    <td class="px-4 py-3 text-right">${asset.area || 'N/A'}</td>
                    <td class="px-4 py-3 text-right">${asset.leaseTerm || 'N/A'}</td>
                    <td class="px-4 py-3 text-right">${asset.topTenantsRatio || 'N/A'}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        function updateRiskWarningTable() {
            const table = document.getElementById('risk-warning-table');
            const warningRatio = parseFloat(document.getElementById('warning-line-ratio').value);
            const stopLossRatio = parseFloat(document.getElementById('stop-loss-line-ratio').value);
            const yellowVar = parseFloat(document.getElementById('var-yellow-threshold').value);
            const redVar = parseFloat(document.getElementById('var-red-threshold').value);
            const yellowConc = parseFloat(document.getElementById('concentration-yellow-threshold').value);
            const redConc = parseFloat(document.getElementById('concentration-red-threshold').value);

            let tableHTML = `<thead><tr><th>代码</th><th>名称</th><th>买入均价</th><th>现价</th><th>持仓金额(万)</th><th>持仓集中度</th><th>VaR值</th><th>预警线</th><th>止损线</th></tr></thead><tbody>`;
            MOCK_DATA.risk_warning.forEach(item => {
                const warningLine = item.buyPrice * warningRatio;
                const stopLossLine = item.buyPrice * stopLossRatio;
                const isWarningTriggered = item.currentPrice <= warningLine;
                const isStopLossTriggered = item.currentPrice <= stopLossLine;

                let concentrationClass = '';
                if (item.concentration >= redConc) concentrationClass = 'bg-red-200';
                else if (item.concentration >= yellowConc) concentrationClass = 'bg-yellow-200';
                
                let varClass = '';
                if (item.var >= redVar) varClass = 'bg-red-200';
                else if (item.var >= yellowVar) varClass = 'bg-yellow-200';

                const rowClass = isStopLossTriggered ? 'bg-red-200' : (isWarningTriggered ? 'bg-yellow-200' : '');

                tableHTML += `<tr class="${rowClass}"><td>${item.code}</td><td>${item.name}</td><td>${item.buyPrice.toFixed(3)}</td><td>${item.currentPrice.toFixed(3)}</td><td>${item.positionValue}</td><td class="${concentrationClass}">${(item.concentration || 0).toFixed(2)}%</td><td class="${varClass}">${(item.var || 0).toFixed(3)}</td><td>${warningLine.toFixed(3)}</td><td>${stopLossLine.toFixed(3)}</td></tr>`;
            });
            table.innerHTML = tableHTML + '</tbody>';
        }

        // --- Error Handling and Debugging ---
        function safeExecute(fn, context = 'Unknown') {
            try {
                return fn();
            } catch (error) {
                console.error(`Error in ${context}:`, error);
                return null;
            }
        }

        function validateChartElement(elementId) {
            const element = document.getElementById(elementId);
            if (!element) {
                console.warn(`Chart element not found: ${elementId}`);
                return false;
            }
            return true;
        }

        

        // --- Main Application Logic ---
        document.addEventListener('DOMContentLoaded', () => {
            financialReitsData = parseFinancialsCSV(document.getElementById('csv-financial-data').textContent);
            console.log('DOM Content Loaded - Starting initialization...');

            // 1. Load and parse data
            const csvElement = document.getElementById('csv-data');
            if (!csvElement) {
                console.error('CSV data element not found!');
                return;
            }

            const csvText = csvElement.textContent;
            console.log('CSV text length:', csvText.length);

            if (!csvText || csvText.trim().length === 0) {
                console.error('CSV data is empty!');
                return;
            }

            const csvData = parseCSV(csvText);
            console.log('Parsed CSV data:', csvData.length, 'rows');

            if (csvData.length === 0) {
                console.error('No data parsed from CSV!');
                return;
            }

            // Initialize MOCK_DATA
            MOCK_DATA.reits = csvData.map(row => ({ id: row['代码'], name: row['证券简称'] }));
            csvData.forEach(row => {
                MOCK_DATA.detailed_info[row['代码']] = row;
            });

            console.log('MOCK_DATA.reits initialized:', MOCK_DATA.reits.length, 'items');
            console.log('MOCK_DATA.detailed_info initialized:', Object.keys(MOCK_DATA.detailed_info).length, 'items');

            // 2. Generate mock data for other sections based on loaded REITs
            MOCK_DATA.reits.forEach(reit => {
                const reitId = reit.id.split('.')[0];
                MOCK_DATA.daily[reitId] = generateDailyData();
                MOCK_DATA.valuation[reitId] = { pffo: 20 + Math.random() * 10, dist_ratio: 3 + Math.random() * 3, mv: 20 + Math.random() * 20, nav_premium: (Math.random() - 0.2) * 20 };
            });
            generateFinancials();

            // Initialize asset operations data
            initAssetsPage();
            
            

            const assetValuationData = {
                '180101': { totalVal: 35.03, details: [ { name: '万海大厦', val: 13.83, price: 25814, tenure: 37.77, rent: '105-280', discount: 0.06, growth: '3.0%-4.0%' } ]},
                '508099': { totalVal: 17.21, details: [ { name: '互联网创新中心', val: 11.65, price: 14182, tenure: 38.64, rent: '4.19-4.43(元/平/天)', discount: 0.06, growth: '2028年起2.75%' } ]}
            };
            MOCK_DATA.asset_valuation = assetValuationData;

            // 3. Setup UI and event listeners
            const navLinks = document.querySelectorAll('#main-nav .nav-link');
            const sections = document.querySelectorAll('#main-content > section');

            console.log('Found navigation links:', navLinks.length);
            console.log('Found sections:', sections.length);

            if (navLinks.length === 0) {
                console.error('No navigation links found!');
                return;
            }

            if (sections.length === 0) {
                console.error('No sections found!');
                return;
            }

            const updateSection = (sectionId) => {
                const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                if (!selectedIds) return;

                switch (sectionId) {
                    case 'section-basic-info':
                        safeExecute(() => renderComparisonGrid(selectedIds), 'Comparison grid update');
                        break;
                    case 'section-asset-valuation':
                        safeExecute(() => updateAssetValuation(selectedIds), 'Asset valuation update');
                        break;
                    case 'section-asset-ops':
                        safeExecute(() => updateAssetOperations(selectedIds), 'Asset operations update');
                        break;
                    case 'section-financials':
                        safeExecute(() => updateFinancials(selectedIds), 'Financial data update');
                        break;
                    case 'section-trading':
                        safeExecute(() => updatePerformanceTable(selectedIds), 'Performance table update');
                        if (MOCK_DATA.daily[selectedIds[0]?.split('.')[0]]) {
                            safeExecute(() => updatePriceTurnoverChart(selectedIds, priceSlider.getRange()), 'Price turnover chart update');
                        }
                        safeExecute(() => updateBubbleChart(selectedIds), 'Bubble chart update');
                        safeExecute(() => updateRadarChart(selectedIds), 'Radar chart update');
                        break;
                    case 'section-risk':
                        safeExecute(() => updateRiskChart(selectedIds), 'Risk chart update');
                        safeExecute(() => updateRiskWarningTable(), 'Risk warning table update');
                        break;
                    // No action needed for reports and backtest on section switch
                    case 'section-reports':
                    case 'section-backtest':
                        break;
                }
            }

            navLinks.forEach((link, index) => {
                const sectionId = link.getAttribute('data-section');
                console.log(`Setting up nav link ${index}: ${sectionId}`);

                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Navigation clicked:', sectionId);

                    // Update active states
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');

                    // Show/hide sections
                    sections.forEach(s => {
                        if (s.id === sectionId) {
                            s.classList.remove('hidden');
                            console.log('Showing section:', s.id);
                        } else {
                            s.classList.add('hidden');
                        }
                    });

                    // Update the content of the newly visible section
                    updateSection(sectionId);
                });
            });

            // 4. Initialize global selector panel
            const globalSelectorPanel = document.getElementById('global-selector-panel');
            if (!globalSelectorPanel) {
                console.error('Global selector panel not found!');
                return;
            }

            const defaultSelection = ['180101.SZ', '508000.SH', '508099.SH'];
            console.log('Initializing global selector with', MOCK_DATA.reits.length, 'REITs');

            globalSelectorPanel.innerHTML = ''; // Clear existing content
            MOCK_DATA.reits.forEach(reit => {
                const label = document.createElement('label');
                label.className = 'block mb-1 cursor-pointer hover:bg-gray-100 p-1 rounded';
                label.innerHTML = `<input type="checkbox" value="${reit.id}" ${defaultSelection.includes(reit.id) ? 'checked' : ''} class="mr-2"> ${reit.name}`;
                globalSelectorPanel.appendChild(label);
            });

            console.log('Global selector initialized with', globalSelectorPanel.children.length, 'items');

            const priceSlider = createDateSlider('price-slider-container', MOCK_DATA.daily[defaultSelection[0].split('.')[0]]?.labels || [], (range) => {
                const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                updatePriceTurnoverChart(selectedIds, range);
            });

            const updateAllVisualizations = () => {
                let selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                // Deduplicate selectedIds
                selectedIds = [...new Set(selectedIds)];
                console.log('DEBUG: selectedIds in updateAllVisualizations:', selectedIds); // Added debug log
                console.log('Updating visualizations for:', selectedIds);

                safeExecute(() => renderComparisonGrid(selectedIds), 'Comparison grid update');
                safeExecute(() => updatePerformanceTable(selectedIds), 'Performance table update');
                safeExecute(() => updateIncomeStructureChart(selectedIds), 'Income structure chart update');
                safeExecute(() => updateFinancials(selectedIds), 'Financial data update');

                if (MOCK_DATA.daily[selectedIds[0]?.split('.')[0]]) {
                    safeExecute(() => updatePriceTurnoverChart(selectedIds, priceSlider.getRange()), 'Price turnover chart update');
                }
                safeExecute(() => updateBubbleChart(selectedIds), 'Bubble chart update');
                safeExecute(() => updateRadarChart(selectedIds), 'Radar chart update');
                safeExecute(() => updateRiskWarningTable(), 'Risk warning table update');
                safeExecute(() => updateAssetValuation(selectedIds), 'Asset valuation update');

                // 更新资产运营模块
                safeExecute(() => updateAssetOperations(selectedIds), 'Asset operations update');
            };

            // 5. Add event listeners to checkboxes
            globalSelectorPanel.addEventListener('change', (e) => {
                if (e.target.type === 'checkbox') {
                    console.log('Checkbox changed:', e.target.value, e.target.checked);
                    updateAllVisualizations();
                }
            });
            
            const categoryFilter = document.getElementById('category-filter');
            fieldConfig.forEach(cat => {
                categoryFilter.innerHTML += `<option value="${cat.category}">${cat.name}</option>`;
            });
            categoryFilter.addEventListener('change', updateAllVisualizations);

            document.getElementById('toggle-differences').addEventListener('click', function() {
                this.classList.toggle('active');
                document.querySelectorAll('.highlight').forEach(el => el.classList.remove('highlight'));
                if (this.classList.contains('active')) {
                    const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                    highlightDifferences(selectedIds);
                }
            });
            
            document.getElementById('export-csv').addEventListener('click', function() {
                const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                const selectedReits = selectedIds.map(id => MOCK_DATA.detailed_info[id]);
                let csv = '分类,字段,' + selectedReits.map(reit => reit['证券简称']).join(',') + '\n';
                
                fieldConfig.forEach(config => {
                    config.fields.forEach(field => {
                        const row = [
                            config.name,
                            field.name,
                            ...selectedReits.map(reit => `"${reit[field.id] || ''}"`)
                        ];
                        csv += row.join(',') + '\n';
                    });
                });
                
                const blob = new Blob([`\uFEFF${csv}`], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'REITs对比数据.csv';
                link.click();
            });

            // 8. Initialize modules
            console.log('Initializing modules...');
            setupReportSection();

            // 初始化资产运营数据
            safeExecute(() => parseAssetOperationsCSV(), 'Asset operations CSV parsing');

            // 初始化因子回测功能
            safeExecute(() => generateFactorInputs(), 'Factor inputs generation');
            safeExecute(() => initBacktestCharts(), 'Backtest charts initialization');
            safeExecute(() => setupBacktestEventListeners(), 'Backtest event listeners setup');

            // 9. Initial Load
            console.log('DEBUG: Before initial updateAllVisualizations call.'); // Added debug log
            console.log('Running initial visualization update...');
            updateAllVisualizations();

            console.log('REITs Analysis Framework initialization completed!');

            // 6. 添加调试功能
            window.debugREITs = {
                testNavigation: () => {
                    console.log('Testing navigation...');
                    const navLinks = document.querySelectorAll('#main-nav .nav-link');
                    const sections = document.querySelectorAll('#main-content > section');
                    console.log('Nav links found:', navLinks.length);
                    console.log('Sections found:', sections.length);

                    navLinks.forEach((link, index) => {
                        console.log(`Link ${index}:`, link.getAttribute('data-section'), link.textContent);
                    });
                },
                testDataLoading: () => {
                    console.log('Testing data loading...');
                    // console.log('CSV data parsed:', csvData?.length || 0);
                    console.log('MOCK_DATA.reits:', MOCK_DATA.reits?.length || 0);
                    console.log('MOCK_DATA.detailed_info keys:', Object.keys(MOCK_DATA.detailed_info || {}).length);
                    console.log('financialReitsData:', financialReitsData?.length || 0);
                    console.log('parsedAssetsData:', parsedAssetsData?.length || 0);
                    // console.log('mockREITs:', mockREITs?.length || 0);

                    // 显示前几个REITs数据
                    if (MOCK_DATA.reits && MOCK_DATA.reits.length > 0) {
                        console.log('First 3 REITs:', MOCK_DATA.reits.slice(0, 3));
                    }
                },
                testSelectors: () => {
                    console.log('Testing selectors...');
                    const globalPanel = document.getElementById('global-selector-panel');
                    console.log('Global selector panel:', !!globalPanel);
                    console.log('Panel innerHTML length:', globalPanel?.innerHTML.length || 0);
                    console.log('Checkboxes:', globalPanel?.querySelectorAll('input[type="checkbox"]').length || 0);

                    if (globalPanel) {
                        console.log('Panel content preview:', globalPanel.innerHTML.substring(0, 200) + '...');
                    }
                },
                switchToSection: (sectionId) => {
                    console.log('Switching to section:', sectionId);
                    const sections = document.querySelectorAll('#main-content > section');
                    sections.forEach(s => s.classList.add('hidden'));
                    const targetSection = document.getElementById(sectionId);
                    if (targetSection) {
                        targetSection.classList.remove('hidden');
                        console.log('Switched to:', sectionId);
                    } else {
                        console.error('Section not found:', sectionId);
                    }
                },
                testCSVParsing: () => {
                    console.log('Testing CSV parsing...');
                    const csvElement = document.getElementById('csv-data');
                    if (csvElement) {
                        const csvText = csvElement.textContent;
                        console.log('CSV text length:', csvText.length);
                        console.log('CSV preview:', csvText.substring(0, 200) + '...');

                        const parsed = parseCSV(csvText);
                        console.log('Parsed rows:', parsed.length);
                        if (parsed.length > 0) {
                            console.log('Headers:', Object.keys(parsed[0]));
                            console.log('First row:', parsed[0]);
                        }
                    } else {
                        console.error('CSV data element not found');
                    }
                }
            };

            console.log('REITs Analysis Framework loaded successfully!');
            console.log('Available debug functions:');
            console.log('- window.debugREITs.testNavigation()');
            console.log('- window.debugREITs.testDataLoading()');
            console.log('- window.debugREITs.testSelectors()');
            console.log('- window.debugREITs.testCSVParsing()');
            console.log('- window.debugREITs.switchToSection(id)');
            console.log('- window.debugREITs.testAllFunctions()');

            // Add comprehensive test function
            window.debugREITs.testAllFunctions = () => {
                console.log('=== Comprehensive Test ===');
                window.debugREITs.testCSVParsing();
                window.debugREITs.testDataLoading();
                window.debugREITs.testNavigation();
                window.debugREITs.testSelectors();
                console.log('=== Test Complete ===');
            };

            // 6. Window resize handler for all charts
            let resizeTimeout;
            window.addEventListener('resize', function() {
                // Debounce resize events
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    // ECharts resize
                    safeExecute(() => {
                        if (ebitdaChart) ebitdaChart.resize();
                        if (distributionChart) distributionChart.resize();
                        if (growthChart) growthChart.resize();
                        if (structureChart) structureChart.resize();
                        if (costChart) costChart.resize();
                    }, 'ECharts resize');

                    // Chart.js charts resize automatically with responsive: true
                    // But we can force update if needed
                    safeExecute(() => {
                        if (priceTurnoverChart) priceTurnoverChart.resize();
                        if (bubbleChart) bubbleChart.resize();
                        if (radarChart) radarChart.resize();
                        if (assetValuationChart) assetValuationChart.resize();

                        // Asset operations charts
                        if (occupancyChart) occupancyChart.resize();
                        if (rentChart) rentChart.resize();
                        if (concentrationChart) concentrationChart.resize();

                        // Backtest charts
                        Object.values(backtestCharts).forEach(chart => {
                            if (chart && chart.resize) chart.resize();
                        });
                    }, 'Chart.js resize');
                }, 250);
            });

            // 7. Initialization complete
            console.log('REITs Analysis Framework initialized successfully');
            console.log('Available modules: Basic Info, Asset Valuation, Asset Operations, Financial Data, Trading, Reports, Risk Warning, Factor Backtest');
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let backtestCharts = {};
        // --- FACTOR BACKTEST CONFIGURATION ---
        const factorsConfig = [
            { id: 'dividend-yield', label: '股息率', defaultWeight: 40, checked: true, isHigherBetter: true, mockIC: 0.15 },
            { id: 'ffo', label: 'FFO增长率', defaultWeight: 35, checked: true, isHigherBetter: true, mockIC: 0.12 },
            { id: 'nav', label: 'NAV折溢价', defaultWeight: 25, checked: true, isHigherBetter: false, mockIC: 0.08 },
            { id: 'debt-ratio', label: '负债比率', defaultWeight: 15, checked: false, isHigherBetter: false, mockIC: -0.05 },
            { id: 'occupancy', label: '出租率', defaultWeight: 10, checked: false, isHigherBetter: true, mockIC: 0.03 }
        ];

        // 基于真实CSV数据的因子回测数据
        const mockREITs = [
            // 从因子回测序列数据.csv中提取的真实REITs数据
            { code: '180101.SZ', name: '博时招商蛇口产业园REIT', type: 'office', 'dividend-yield': 6.64, ffo: 26.68, nav: 0.16, 'debt-ratio': 32.1, occupancy: 91.2 },
            { code: '508000.SH', name: '华安张江产业园REIT', type: 'office', 'dividend-yield': 5.1, ffo: 6.8, nav: 0.8, 'debt-ratio': 28.5, occupancy: 93.4 },

            // 其他REITs的模拟数据
            { code: '180102.SZ', name: '华夏合肥高新产园REIT', type: 'office', 'dividend-yield': 4.6, ffo: 6.5, nav: -0.8, 'debt-ratio': 35.8, occupancy: 88.9 },
            { code: '180103.SZ', name: '华夏杭州和达高科产园REIT', type: 'office', 'dividend-yield': 4.7, ffo: 7.1, nav: -1.2, 'debt-ratio': 33.4, occupancy: 90.3 },
            { code: '508021.SH', name: '国泰君安临港创新产业园REIT', type: 'industrial', 'dividend-yield': 5.8, ffo: 9.1, nav: -3.2, 'debt-ratio': 26.8, occupancy: 96.1 },
            { code: '508088.SH', name: '国泰君安东久新经济REIT', type: 'industrial', 'dividend-yield': 6.1, ffo: 10.2, nav: -5.3, 'debt-ratio': 28.7, occupancy: 95.2 },
            { code: '508056.SH', name: '中金普洛斯仓储物流REIT', type: 'industrial', 'dividend-yield': 5.5, ffo: 8.8, nav: -2.1, 'debt-ratio': 30.2, occupancy: 98.5 },
            { code: '508001.SH', name: '红土盐田港仓储物流REIT', type: 'industrial', 'dividend-yield': 5.3, ffo: 8.2, nav: -1.8, 'debt-ratio': 29.8, occupancy: 97.2 },
            { code: '508097.SH', name: '华泰紫金南京建邺产业园REIT', type: 'office', 'dividend-yield': 4.9, ffo: 7.5, nav: -0.5, 'debt-ratio': 33.1, occupancy: 89.8 },
            { code: '508010.SH', name: '中金重庆两江产业园REIT', type: 'office', 'dividend-yield': 4.8, ffo: 7.0, nav: -1.1, 'debt-ratio': 34.5, occupancy: 88.7 }
        ];

        console.log('Mock REITs data loaded:', mockREITs.length, 'items');

        // 基于真实CSV数据的历史序列数据
        const mockHistoryBase = {
            // 生成从2021年6月到2024年12月的月度数据
            dates: Array.from({ length: 43 }, (_, i) => {
                const startDate = new Date(2021, 5, 1); // 2021年6月
                const currentDate = new Date(startDate);
                currentDate.setMonth(startDate.getMonth() + i);
                return `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
            }),
            // 基于真实REITs表现生成的组合净值曲线
            portfolioValue: Array.from({ length: 43 }, (_, i) => {
                const baseValue = 100;
                const trend = i * 0.8; // 长期上升趋势
                const volatility = Math.sin(i / 6) * 8 + Math.random() * 4 - 2; // 市场波动
                const marketShock = i === 20 ? -12 : (i === 35 ? -8 : 0); // 市场冲击
                return Math.max(80, baseValue + trend + volatility + marketShock);
            }),
            // 基准指数表现（相对保守）
            benchmarkValue: Array.from({ length: 43 }, (_, i) => {
                const baseValue = 100;
                const trend = i * 0.6; // 基准较慢增长
                const volatility = Math.cos(i / 8) * 5 + Math.random() * 3 - 1.5;
                const marketShock = i === 20 ? -10 : (i === 35 ? -6 : 0);
                return Math.max(85, baseValue + trend + volatility + marketShock);
            })
        };

        // Factor backtest initialization functions (will be called from main init)

        function generateFactorInputs() {
            const container = document.getElementById('factor-container');
            if (!container) return;

            let html = '';
            factorsConfig.forEach(factor => {
                html += `
                    <div class="col-md-6">
                        <div class="factor-metric-card">
                            <div class="form-check d-flex justify-content-between align-items-center">
                                <div>
                                    <input class="form-check-input metric-check" type="checkbox" id="${factor.id}" ${factor.checked ? 'checked' : ''}>
                                    <label class="form-check-label" for="${factor.id}">${factor.label}</label>
                                </div>
                                <div class="d-flex align-items-center">
                                    <input type="number" class="form-control form-control-sm factor-weight" value="${factor.defaultWeight}" min="0" max="100" data-factor-id="${factor.id}" ${!factor.checked ? 'disabled' : ''}>
                                    <span class="ms-1">%</span>
                                </div>
                            </div>
                        </div>
                    </div>`;
            });
            container.innerHTML = html;
        }

        function initBacktestCharts() {
            const chartConfigs = {
                'performance-chart': { type: 'line', title: '净值曲线对比', datasets: [{ label: '策略净值', borderColor: '#3498db', backgroundColor: 'rgba(52, 152, 219, 0.1)', fill: true }, { label: '基准净值', borderColor: '#95a5a6', backgroundColor: 'rgba(149, 165, 166, 0.1)', fill: true }] },
                'monthly-return-chart': { type: 'bar', title: '月度收益率对比', datasets: [{ label: '策略月度收益', backgroundColor: 'rgba(52, 152, 219, 0.7)' }, { label: '基准月度收益', backgroundColor: 'rgba(149, 165, 166, 0.7)' }] },
                'factor-contribution-chart': { type: 'doughnut', title: '因子贡献度', datasets: [{ data: [], backgroundColor: ['#3498db', '#e74c3c', '#2ecc71', '#f1c40f', '#9b59b6', '#34495e'] }] },
                'factor-correlation-chart': { type: 'radar', title: '因子IC分析', datasets: [{ label: 'IC均值', data: [], backgroundColor: 'rgba(52, 152, 219, 0.2)', borderColor: '#3498db' }] }
            };

            Object.keys(chartConfigs).forEach(id => {
                const element = document.getElementById(id);
                if (!element) return;

                const config = chartConfigs[id];
                const ctx = element.getContext('2d');
                backtestCharts[id] = new Chart(ctx, {
                    type: config.type,
                    data: { labels: [], datasets: config.datasets.map(ds => ({ ...ds, data: [] })) },
                    options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: config.title, font: { size: 16 } } } }
                });
            });
        }

        function setupBacktestEventListeners() {
            const runButton = document.getElementById('run-backtest');
            const exportButton = document.getElementById('export-results');
            const factorContainer = document.getElementById('factor-container');

            if (runButton) runButton.addEventListener('click', runBacktest);
            if (exportButton) exportButton.addEventListener('click', () => alert('导出功能待实现'));

            if (factorContainer) {
                factorContainer.addEventListener('change', (e) => {
                    if (e.target.matches('.metric-check, .factor-weight')) {
                        const checkbox = e.target.closest('.factor-metric-card').querySelector('.metric-check');
                        const weightInput = e.target.closest('.factor-metric-card').querySelector('.factor-weight');
                        weightInput.disabled = !checkbox.checked;
                        updateTotalWeight();
                    }
                });
            }
        }

        function updateTotalWeight() {
            let totalWeight = 0;
            document.querySelectorAll('.metric-check:checked').forEach(checkbox => {
                const weightInput = checkbox.closest('.factor-metric-card').querySelector('.factor-weight');
                totalWeight += parseFloat(weightInput.value) || 0;
            });
            const totalEl = document.getElementById('weight-total');
            if (totalEl) {
                totalEl.textContent = `${totalWeight.toFixed(0)}%`;
                totalEl.style.color = totalWeight === 100 ? 'var(--primary)' : 'var(--accent-color)';
            }
        }

        function setValidationAlert(message, type = 'danger') {
            const alertPlaceholder = document.getElementById('validation-alert');
            if (!alertPlaceholder) return;

            if (message) {
                alertPlaceholder.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>`;
            } else {
                alertPlaceholder.innerHTML = '';
            }
        }

        function getBacktestParams() {
            const reitTypeElement = document.getElementById('reit-type');
            const timePeriodElement = document.getElementById('time-period');
            const holdingCountElement = document.getElementById('holding-count');

            const params = {
                reitTypes: reitTypeElement ? Array.from(reitTypeElement.selectedOptions).map(opt => opt.value) : ['retail', 'office'],
                timePeriod: timePeriodElement ? timePeriodElement.value : '5y',
                holdingCount: holdingCountElement ? parseInt(holdingCountElement.value) : 10,
                factorWeights: {}
            };

            let totalWeight = 0;
            document.querySelectorAll('.metric-check:checked').forEach(checkbox => {
                const weightInput = checkbox.parentElement.parentElement.querySelector('.factor-weight');
                const weight = parseFloat(weightInput.value);
                params.factorWeights[checkbox.id] = weight / 100;
                totalWeight += weight;
            });

            if (Math.round(totalWeight) !== 100) {
                 throw new Error(`因子权重总和必须为100%，当前为 ${totalWeight.toFixed(0)}%。`);
            }
            return params;
        }

        function performBacktestCalculations(params) {
            const periods = { '1y': 4, '3y': 12, '5y': 20, '10y': 40 };
            const numPoints = periods[params.timePeriod] || mockHistoryBase.dates.length;
            const dates = mockHistoryBase.dates.slice(-numPoints);

            let performanceBoost = 0;
            factorsConfig.forEach(factor => {
                const weight = params.factorWeights[factor.id] || 0;
                performanceBoost += (weight - (factor.defaultWeight / 100)) * factor.mockIC;
            });

            const portfolioValue = mockHistoryBase.portfolioValue.slice(-numPoints).map(v => v * (1 + performanceBoost));
            const benchmarkValue = mockHistoryBase.benchmarkValue.slice(-numPoints);

            const startValue = portfolioValue[0];
            const endValue = portfolioValue[portfolioValue.length - 1];
            const years = dates.length / 4;

            const totalReturn = ((endValue / startValue) - 1) * 100;
            const annualizedReturn = (Math.pow(endValue / startValue, 1 / years) - 1) * 100;

            let maxDrawdown = 0, peak = -Infinity;
            portfolioValue.forEach(value => {
                peak = Math.max(peak, value);
                maxDrawdown = Math.max(maxDrawdown, (peak - value) / peak * 100);
            });

            const monthlyReturns = [], benchmarkMonthlyReturns = [];
            for (let i = 1; i < portfolioValue.length; i++) {
                monthlyReturns.push(((portfolioValue[i] / portfolioValue[i-1]) - 1) * 100);
                benchmarkMonthlyReturns.push(((benchmarkValue[i] / benchmarkValue[i-1]) - 1) * 100);
            }

            const scoredREITs = mockREITs
                .filter(reit => params.reitTypes.includes(reit.type))
                .map(reit => {
                    let score = 0;
                    factorsConfig.forEach(factor => {
                        const weight = params.factorWeights[factor.id];
                        if (weight > 0) {
                            const value = reit[factor.id] || 0;
                            score += (factor.isHigherBetter ? value : -value) * weight;
                        }
                    });
                    return { ...reit, score: score.toFixed(2) };
                })
                .sort((a, b) => b.score - a.score);

            const latestPortfolio = scoredREITs.slice(0, params.holdingCount);

            return { dates, portfolioValue, benchmarkValue, totalReturn, annualizedReturn, maxDrawdown, monthlyReturns, benchmarkMonthlyReturns, latestPortfolio };
        }

        function updateBacktestUI(results, params) {
            // Update metric cards
            const totalReturnEl = document.getElementById('total-return');
            const annualizedReturnEl = document.getElementById('annualized-return');
            const maxDrawdownEl = document.getElementById('max-drawdown');

            if (totalReturnEl) totalReturnEl.textContent = `${results.totalReturn.toFixed(2)}%`;
            if (annualizedReturnEl) annualizedReturnEl.textContent = `${results.annualizedReturn.toFixed(2)}%`;
            if (maxDrawdownEl) maxDrawdownEl.textContent = `${results.maxDrawdown.toFixed(2)}%`;

            // Update charts
            if (backtestCharts['performance-chart']) {
                backtestCharts['performance-chart'].data.labels = results.dates;
                backtestCharts['performance-chart'].data.datasets[0].data = results.portfolioValue;
                backtestCharts['performance-chart'].data.datasets[1].data = results.benchmarkValue;
                backtestCharts['performance-chart'].update();
            }

            if (backtestCharts['monthly-return-chart']) {
                backtestCharts['monthly-return-chart'].data.labels = results.dates.slice(1);
                backtestCharts['monthly-return-chart'].data.datasets[0].data = results.monthlyReturns;
                backtestCharts['monthly-return-chart'].data.datasets[1].data = results.benchmarkMonthlyReturns;
                backtestCharts['monthly-return-chart'].update();
            }

            // Update portfolio table
            const portfolioBody = document.querySelector('#portfolio-table tbody');
            if (portfolioBody) {
                portfolioBody.innerHTML = results.latestPortfolio.map(reit => `
                    <tr>
                        <td>${reit.code}</td><td>${reit.name}</td><td>${reit.type}</td>
                        <td>${(100 / params.holdingCount).toFixed(2)}%</td>
                        <td>${reit['dividend-yield']}%</td><td>${reit.ffo}%</td><td>${reit.nav}%</td>
                        <td>${reit.score}</td>
                    </tr>
                `).join('');
            }

            // Update factor analysis
            const activeFactors = factorsConfig.filter(f => params.factorWeights[f.id] > 0);

            if (backtestCharts['factor-contribution-chart']) {
                backtestCharts['factor-contribution-chart'].data.labels = activeFactors.map(f => f.label);
                backtestCharts['factor-contribution-chart'].data.datasets[0].data = activeFactors.map(f => params.factorWeights[f.id] * 100);
                backtestCharts['factor-contribution-chart'].update();
            }

            if (backtestCharts['factor-correlation-chart']) {
                backtestCharts['factor-correlation-chart'].data.labels = activeFactors.map(f => f.label);
                backtestCharts['factor-correlation-chart'].data.datasets[0].data = activeFactors.map(f => f.mockIC);
                backtestCharts['factor-correlation-chart'].update();
            }

            const factorMetricsBody = document.getElementById('factor-metrics-table');
            if (factorMetricsBody) {
                factorMetricsBody.innerHTML = activeFactors.map(f => `
                    <tr>
                        <td>${f.label}</td><td>${f.mockIC.toFixed(2)}</td><td>${(f.mockIC * 2.5).toFixed(2)}</td><td>${(50 + f.mockIC * 100).toFixed(0)}%</td>
                    </tr>
                `).join('');
            }
        }

        function runBacktest() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) loadingIndicator.style.display = 'block';
            setValidationAlert(''); // Clear previous alerts

            setTimeout(() => {
                try {
                    const params = getBacktestParams();
                    const results = performBacktestCalculations(params);
                    updateBacktestUI(results, params);
                } catch (error) {
                    console.error('Backtest Error:', error);
                    setValidationAlert(error.message);
                } finally {
                    if (loadingIndicator) loadingIndicator.style.display = 'none';
                }
            }, 500);
        }

        
    </script>
</body>
</html>