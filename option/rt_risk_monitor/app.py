import pandas as pd
import numpy as np
import threading
import time
import os
from datetime import datetime
from flask import Flask, jsonify, render_template, request
from data import initialize_system, get_realtime_option_data, shutdown_system, reload_system_with_new_file
from greeks_calculator import PyVollibNumbaGreeksCalculator
from option_greeks import op_porf, op_greeks_sum
import atexit
import config

# 初始化 Flask 应用
app = Flask(__name__)

# 初始化希腊字母计算器
greeks_calculator = PyVollibNumbaGreeksCalculator()

# 全局变量存储最新计算结果
latest_greeks_data = []
data_lock = threading.Lock()
system_initialized = False
current_data_file = config.DATA_FILE  # 当前使用的数据文件

# 常量从配置文件读取
INTEREST_RATE = config.INTEREST_RATE
PRICE_PCT = config.PRICE_PCT

def calculate_option_greeks(option_data):
    """计算期权希腊字母"""
    if option_data.empty:
        return pd.DataFrame()
    
    try:
        # 提取数据
        symbols = option_data['symbol'].tolist()
        underlyings = option_data['underlying_code'].tolist()
        positions = option_data['position'].values.astype(np.float64)
        K_values = option_data['k'].values.astype(np.float64)
        t_values_years = option_data['t'].values.astype(np.float64)
        r_values = np.full(len(option_data), INTEREST_RATE, dtype=np.float64)
        flags = option_data['sign'].values.astype(np.int32)
        mults = option_data['mult'].values.astype(np.float64)
        S_values = option_data['S'].values.astype(np.float64)
        option_prices = option_data['op_price'].values.astype(np.float64)
        
        # 过滤无效数据
        valid_mask = (S_values > 0) & (option_prices > 0) & (t_values_years > 0)
        if not valid_mask.any():
            print("没有有效的期权数据")
            return pd.DataFrame()
        
        # 只处理有效数据
        valid_indices = np.where(valid_mask)[0]
        
        # 1. 计算隐含波动率
        iv_values = np.full(len(option_data), 0.2)  # 默认值
        if len(valid_indices) > 0:
            valid_iv = greeks_calculator.calculate_implied_volatility_batch(
                S_values[valid_indices], K_values[valid_indices], 
                t_values_years[valid_indices], r_values[valid_indices],
                option_prices[valid_indices], flags[valid_indices]
            )
            iv_values[valid_indices] = valid_iv
        
        # 2. 计算标准希腊字母
        greeks_results = greeks_calculator.calculate_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, flags
        )
        
        # 3. 计算现金希腊字母
        cash_greeks_results = greeks_calculator.calculate_cash_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, 
            flags, positions, mults
        )
        
        # 4. 计算其他指标
        premium_totals_wan = option_prices * mults * np.abs(positions) / 10000.0
        # 修正Gamma效应计算：cash_gamma已经是万元单位，需要乘以100恢复到原始单位再计算
        gamma_effect = 0.5 * cash_greeks_results['cash_gamma'] * 100 * PRICE_PCT ** 2
        stPnl_up = cash_greeks_results['cash_delta'] * PRICE_PCT + gamma_effect
        stPnl_down = -cash_greeks_results['cash_delta'] * PRICE_PCT + gamma_effect
        
        # 5. 构建结果DataFrame，确保数据类型兼容JSON序列化
        result_df = pd.DataFrame({
            'symbol': symbols,
            'underlying': underlyings,
            'position': positions.astype(float),
            'oprice': option_prices.astype(float),
            'k': K_values.astype(float),
            't': (t_values_years * 365).astype(float),  # 转换回天数
            't_years': t_values_years.astype(float),
            'sign': flags.astype(int),
            'mult': mults.astype(float),
            'price': S_values.astype(float),
            'IV': iv_values.astype(float),
            'delta': greeks_results['delta'].astype(float),
            'gamma': greeks_results['gamma'].astype(float),
            'vega': greeks_results['vega'].astype(float),
            'theta': greeks_results['theta'].astype(float),
            'rho': greeks_results['rho'].astype(float),
            '$delta': cash_greeks_results['cash_delta'].astype(float),
            '$gamma': (cash_greeks_results['cash_gamma'] / 100.0).astype(float),  # 转换为万元
            '$vega': cash_greeks_results['cash_vega'].astype(float),
            '$theta': cash_greeks_results['cash_theta'].astype(float),
            '$rho': cash_greeks_results['cash_rho'].astype(float),
            'premium': premium_totals_wan.astype(float),
            'stPnl_up': stPnl_up.astype(float),
            'stPnl_down': stPnl_down.astype(float)
        })

        # 6. 清理无效值，确保JSON序列化兼容性
        # 将NaN、Infinity等无效值替换为0或合理的默认值
        numeric_columns = result_df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            # 替换NaN和无穷大值
            result_df[col] = result_df[col].replace([np.inf, -np.inf], 0)
            result_df[col] = result_df[col].fillna(0)

        return result_df
        
    except Exception as e:
        print(f"计算希腊字母时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def generate_excel_reports(adjusted_data=None):
    """生成Excel报告和图片，使用调整后的position数据"""
    try:
        current_time = datetime.now()
        today_str = current_time.strftime("%Y%m%d")

        # 确保out目录存在
        out_dir = config.OUTPUT_DIR
        if not os.path.exists(out_dir):
            os.makedirs(out_dir)

        detail_file = os.path.join(out_dir, f'single_contracts_detail_{today_str}.xlsx')
        summary_file = os.path.join(out_dir, f'underlying_summary_{today_str}.xlsx')

        print(f"开始生成 {today_str} 的报告...")

        # 使用调整后的数据或当前数据
        if adjusted_data is not None:
            greeks_data = pd.DataFrame(adjusted_data)
            print("使用HTML界面调整后的position数据生成报告")
        else:
            # 获取当前数据并计算希腊字母
            option_data = get_realtime_option_data()
            if not option_data.empty:
                greeks_data = calculate_option_greeks(option_data)
            else:
                print("没有可用的期权数据")
                return False, "没有可用的期权数据"

        if not greeks_data.empty:
            # 生成详细报告
            greeks_data.to_excel(detail_file, index=False)
            print(f"已生成详细合约报告: {detail_file}")

            # 生成汇总报告
            summary_data = calculate_summary_for_report(greeks_data)
            if summary_data is not None:
                summary_data.to_excel(summary_file, index=True)
                print(f"已生成汇总报告: {summary_file}")

                # 生成图片报告
                try:
                    generate_summary_image(summary_data, greeks_data, today_str, out_dir)
                    print(f"已生成图片报告")
                except Exception as img_error:
                    print(f"生成图片时出错: {str(img_error)}")
                    # 图片生成失败不影响整体报告生成

                return True, f"报告生成成功：Excel文件和图片已保存到 {out_dir} 目录"

        return False, "数据为空，无法生成报告"

    except Exception as e:
        print(f"生成报告时出错: {str(e)}")
        return False, f"生成报告时出错: {str(e)}"

def generate_summary_image(summary_data, detail_data, today_str, out_dir):
    """生成汇总数据的图片报告"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        from matplotlib import rcParams
        import numpy as np

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建更复杂的图表布局
        fig = plt.figure(figsize=(16, 12))
        gs = fig.add_gridspec(3, 2, height_ratios=[1.5, 1, 1], width_ratios=[1, 1])

        fig.suptitle(f'期权风险监控报告 - {today_str}', fontsize=18, fontweight='bold')

        # 1. 汇总表格 (占据上半部分)
        ax1 = fig.add_subplot(gs[0, :])
        ax1.axis('tight')
        ax1.axis('off')
        ax1.set_title('标的汇总', fontsize=14, fontweight='bold', pad=20)

        # 准备汇总表格数据
        summary_display = summary_data.copy()
        summary_display = summary_display.round(4)

        # 创建表格
        table1 = ax1.table(cellText=summary_display.values,
                          rowLabels=summary_display.index,
                          colLabels=summary_display.columns,
                          cellLoc='center',
                          loc='center')
        table1.auto_set_font_size(False)
        table1.set_fontsize(9)
        table1.scale(1.2, 1.5)

        # 为表格添加颜色
        for i, (idx, row) in enumerate(summary_display.iterrows()):
            if idx == 'Total':
                # 总计行使用不同颜色
                for j in range(len(summary_display.columns)):
                    table1[(i+1, j)].set_facecolor('#E6E6FA')
            else:
                # 根据风险值设置颜色
                delta_val = abs(row.get('$delta', 0))
                gamma_val = abs(row.get('$gamma', 0))
                if delta_val > 100 or gamma_val > 50:  # 高风险
                    for j in range(len(summary_display.columns)):
                        table1[(i+1, j)].set_facecolor('#FFE4E1')

        # 2. 风险指标对比图 (左下)
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.set_title('主要风险指标对比', fontsize=12, fontweight='bold')

        if 'Total' in summary_data.index:
            total_row = summary_data.loc['Total']
            risk_metrics = ['$delta', '$gamma', '$vega']
            risk_values = [total_row.get(metric, 0) for metric in risk_metrics]
            risk_labels = ['Delta', 'Gamma', 'Vega']

            # 使用不同颜色表示正负值
            colors = ['#FF6B6B' if v < 0 else '#4ECDC4' for v in risk_values]
            bars = ax2.bar(risk_labels, risk_values, color=colors, alpha=0.8)

            ax2.set_ylabel('金额 (万元)')
            ax2.grid(True, alpha=0.3, axis='y')
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # 在柱子上显示数值
            for bar, value in zip(bars, risk_values):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2.,
                        height + (5 if height >= 0 else -15),
                        f'{value:.1f}',
                        ha='center', va='bottom' if height >= 0 else 'top',
                        fontweight='bold')

        # 3. 标的分布饼图 (右下)
        ax3 = fig.add_subplot(gs[1, 1])
        ax3.set_title('标的权利金分布', fontsize=12, fontweight='bold')

        # 排除Total行，计算各标的的权利金占比
        underlying_data = summary_data.drop('Total', errors='ignore')
        if not underlying_data.empty and 'premium' in underlying_data.columns:
            premium_values = underlying_data['premium'].abs()
            premium_values = premium_values[premium_values > 0]  # 只显示有权利金的标的

            if len(premium_values) > 0:
                # 如果标的太多，只显示前6个，其余合并为"其他"
                if len(premium_values) > 6:
                    top_5 = premium_values.nlargest(5)
                    others_sum = premium_values.drop(top_5.index).sum()
                    plot_data = pd.concat([top_5, pd.Series([others_sum], index=['其他'])])
                else:
                    plot_data = premium_values

                colors = plt.cm.Set3(np.linspace(0, 1, len(plot_data)))
                wedges, texts, autotexts = ax3.pie(plot_data.values,
                                                  labels=plot_data.index,
                                                  autopct='%1.1f%%',
                                                  colors=colors,
                                                  startangle=90)

                # 美化文字
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')

        # 4. 压力测试结果 (底部)
        ax4 = fig.add_subplot(gs[2, :])
        ax4.set_title('压力测试结果 (3%价格变动)', fontsize=12, fontweight='bold')

        if not underlying_data.empty:
            underlyings = underlying_data.index.tolist()
            stpnl_up = underlying_data['stPnl_up'].values
            stpnl_down = underlying_data['stPnl_down'].values

            x = np.arange(len(underlyings))
            width = 0.35

            bars1 = ax4.bar(x - width/2, stpnl_up, width, label='上涨3%',
                           color='#2ECC71', alpha=0.8)
            bars2 = ax4.bar(x + width/2, stpnl_down, width, label='下跌3%',
                           color='#E74C3C', alpha=0.8)

            ax4.set_xlabel('标的代码')
            ax4.set_ylabel('盈亏 (万元)')
            ax4.set_xticks(x)
            ax4.set_xticklabels(underlyings, rotation=45, ha='right')
            ax4.legend()
            ax4.grid(True, alpha=0.3, axis='y')
            ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)

            # 在柱子上显示数值
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    if abs(height) > 1:  # 只显示绝对值大于1的数值
                        ax4.text(bar.get_x() + bar.get_width()/2.,
                                height + (2 if height >= 0 else -5),
                                f'{height:.1f}',
                                ha='center', va='bottom' if height >= 0 else 'top',
                                fontsize=8)

        plt.tight_layout()

        # 保存图片
        image_file = os.path.join(out_dir, f'risk_summary_{today_str}.png')
        plt.savefig(image_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        print(f"已生成增强版图片报告: {image_file}")
        return True

    except ImportError:
        print("警告：matplotlib未安装，跳过图片生成")
        return False
    except Exception as e:
        print(f"生成图片时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_and_generate_daily_reports():
    """检查是否需要生成每日报告（15点后）"""
    current_time = datetime.now()
    current_hour = current_time.hour
    
    # 检查是否是15点后且还没有生成今日报告
    if current_hour >= 15:
        today_str = current_time.strftime("%Y%m%d")
        out_dir = config.OUTPUT_DIR
        detail_file = os.path.join(out_dir, f'single_contracts_detail_{today_str}.xlsx')
        summary_file = os.path.join(out_dir, f'underlying_summary_{today_str}.xlsx')
        
        # 检查文件是否已存在
        if not os.path.exists(detail_file) or not os.path.exists(summary_file):
            generate_excel_reports()

def calculate_summary_for_report(greeks_data):
    """计算汇总数据用于报告"""
    try:
        # 按标的分组汇总
        agg_funcs = {
            'position': 'sum',
            '$delta': 'sum',
            '$gamma': 'sum',
            '$vega': 'sum',
            'premium': 'sum',
            'stPnl_up': 'sum',
            'stPnl_down': 'sum'
        }
        
        summary = greeks_data.groupby('underlying').agg(agg_funcs)
        
        # 计算加权平均IV
        def weighted_iv(group):
            weights = np.abs(group['$vega'])
            if weights.sum() != 0:
                return np.average(group['IV'], weights=weights)
            return np.nan
        
        iv_summary = greeks_data.groupby('underlying').apply(weighted_iv)
        summary['IV'] = iv_summary
        
        # 按$gamma排序
        summary = summary.sort_values(by='$gamma', ascending=True)
        
        # 添加总计行
        total_row = pd.DataFrame({
            'position': [summary['position'].sum()],
            '$delta': [summary['$delta'].sum()],
            '$gamma': [summary['$gamma'].sum()],
            '$vega': [summary['$vega'].sum()],
            'premium': [summary['premium'].sum()],
            'stPnl_up': [summary['stPnl_up'].sum()],
            'stPnl_down': [summary['stPnl_down'].sum()],
            'IV': [np.average(greeks_data['IV'], weights=np.abs(greeks_data['$vega'])) 
                   if np.sum(np.abs(greeks_data['$vega'])) != 0 else np.nan]
        }, index=['Total'])
        
        final_summary = pd.concat([summary, total_row])
        return final_summary
        
    except Exception as e:
        print(f"计算汇总数据时出错: {str(e)}")
        return None

def update_greeks_continuously():
    """持续更新希腊字母计算"""
    global latest_greeks_data
    last_report_check = datetime.now().date()
    update_count = 0
    total_calculation_time = 0
    last_performance_report = time.time()

    print("开始持续更新希腊字母计算线程")

    while system_initialized:
        try:
            update_count += 1
            start_time = time.time()

            # 获取实时数据
            option_data = get_realtime_option_data()

            if not option_data.empty:
                data_fetch_time = time.time() - start_time

                # 计算希腊字母
                calc_start_time = time.time()
                greeks_data = calculate_option_greeks(option_data)
                calc_time = time.time() - calc_start_time

                total_calculation_time += calc_time

                if not greeks_data.empty:
                    with data_lock:
                        latest_greeks_data = greeks_data.to_dict('records')

                    total_time = time.time() - start_time
                    print(f"第{update_count}次更新: 获取{len(option_data)}条数据({data_fetch_time:.3f}s), "
                          f"计算{len(latest_greeks_data)}个希腊字母({calc_time:.3f}s), "
                          f"总耗时{total_time:.3f}s")

                    # 检查是否需要生成每日报告（每天只检查一次）
                    current_date = datetime.now().date()
                    if current_date != last_report_check:
                        check_and_generate_daily_reports()
                        last_report_check = current_date
                else:
                    print(f"第{update_count}次更新: 希腊字母计算结果为空")
            else:
                print(f"第{update_count}次更新: 实时数据为空")

            # 每100次更新输出性能统计
            if update_count % 100 == 0:
                avg_calc_time = total_calculation_time / update_count
                current_time = time.time()
                time_since_last_report = current_time - last_performance_report
                print(f"\n=== 性能统计 (第{update_count}次更新) ===")
                print(f"平均计算时间: {avg_calc_time:.3f}s")
                print(f"最近100次更新耗时: {time_since_last_report:.1f}s")
                print(f"更新频率: {100/time_since_last_report:.1f} 次/秒")
                print("=" * 40)
                last_performance_report = current_time

        except Exception as e:
            print(f"第{update_count}次更新希腊字母时出错: {str(e)}")
            import traceback
            traceback.print_exc()

        time.sleep(config.REALTIME_UPDATE_INTERVAL)  # 每秒更新一次

def initialize_app(data_file=None):
    """初始化应用"""
    global system_initialized, current_data_file
    
    if data_file:
        current_data_file = data_file
    
    print("正在初始化期权风险监控系统...")
    
    # 初始化数据系统
    if initialize_system(current_data_file):
        system_initialized = True
        print("数据系统初始化成功")
        
        # 启动后台线程持续更新希腊字母
        update_thread = threading.Thread(target=update_greeks_continuously, daemon=True)
        update_thread.start()
        print("希腊字母更新线程已启动")
        
        return True
    else:
        print("数据系统初始化失败")
        return False

def reinitialize_with_new_file(file_path):
    """使用新文件重新初始化系统"""
    global system_initialized, current_data_file, latest_greeks_data
    
    print(f"使用新文件重新初始化系统: {file_path}")
    
    # 停止当前系统
    system_initialized = False
    time.sleep(config.SYSTEM_REINIT_WAIT_TIME)  # 等待更新线程停止
    
    # 清空当前数据
    with data_lock:
        latest_greeks_data = []
    
    # 使用新文件重新加载
    if reload_system_with_new_file(file_path):
        current_data_file = file_path
        system_initialized = True
        
        # 重新启动更新线程
        update_thread = threading.Thread(target=update_greeks_continuously, daemon=True)
        update_thread.start()
        
        print("系统重新初始化成功")
        return True
    else:
        print("系统重新初始化失败")
        return False

@app.route('/')
def index():
    """提供主页面"""
    return render_template('index.html')

def clean_data_for_json(data):
    """清理数据以确保JSON序列化兼容性"""
    import json
    import math

    def clean_value(value):
        if isinstance(value, (int, float)):
            if math.isnan(value) or math.isinf(value):
                return 0.0
            return float(value)
        return value

    if isinstance(data, list):
        return [clean_data_for_json(item) for item in data]
    elif isinstance(data, dict):
        return {key: clean_data_for_json(value) for key, value in data.items()}
    else:
        return clean_value(data)

def apply_position_overrides(data):
    """应用用户修改的持仓数量"""
    global user_position_overrides

    if not user_position_overrides:
        return data

    # 创建数据副本以避免修改原始数据
    modified_data = []

    for item in data:
        item_copy = item.copy()
        symbol = item_copy.get('symbol')

        if symbol in user_position_overrides:
            # 应用用户修改的持仓数量
            new_position = user_position_overrides[symbol]
            original_position = float(item_copy.get('position', 0))

            if new_position != original_position:
                # 重新计算现金希腊字母
                position_ratio = new_position / original_position if original_position != 0 else 0

                # 更新持仓数量
                item_copy['position'] = new_position

                # 重新计算现金希腊字母（保持单位希腊字母不变）
                if position_ratio != 0:
                    for key in ['$delta', '$gamma', '$vega', '$theta', '$rho', 'premium', 'stPnl_up', 'stPnl_down']:
                        if key in item_copy:
                            original_value = float(item_copy[key])
                            item_copy[key] = original_value * position_ratio

        modified_data.append(item_copy)

    return modified_data

@app.route('/api/data')
def get_data():
    """提供实时期权希腊字母数据的API端点"""
    global latest_greeks_data

    try:
        with data_lock:
            if latest_greeks_data:
                print(f"API返回数据: {len(latest_greeks_data)} 条记录")

                # 应用用户修改的持仓数量
                data_with_overrides = apply_position_overrides(latest_greeks_data)

                # 清理数据确保JSON兼容性
                cleaned_data = clean_data_for_json(data_with_overrides)
                return jsonify(cleaned_data)
            else:
                print("API返回空数据")
                # 如果没有数据，尝试立即获取一次
                if system_initialized:
                    option_data = get_realtime_option_data()
                    if not option_data.empty:
                        greeks_data = calculate_option_greeks(option_data)
                        if not greeks_data.empty:
                            latest_greeks_data = greeks_data.to_dict('records')
                            print(f"即时计算返回数据: {len(latest_greeks_data)} 条记录")

                            # 应用用户修改的持仓数量
                            data_with_overrides = apply_position_overrides(latest_greeks_data)

                            # 清理数据确保JSON兼容性
                            cleaned_data = clean_data_for_json(data_with_overrides)
                            return jsonify(cleaned_data)

                return jsonify([])
    except Exception as e:
        print(f"API数据获取错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

# 用户修改的持仓数量存储
user_position_overrides = {}

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    global latest_greeks_data

    # 获取系统运行时间
    current_time = time.time()

    # 计算数据新鲜度
    data_freshness = "未知"
    if latest_greeks_data:
        # 假设数据每2秒更新一次
        expected_update_interval = config.REALTIME_UPDATE_INTERVAL
        time_since_last_update = current_time % expected_update_interval
        data_freshness = f"{time_since_last_update:.1f}秒前"

    return jsonify({
        'initialized': system_initialized,
        'data_count': len(latest_greeks_data) if latest_greeks_data else 0,
        'last_update': current_time,
        'data_freshness': data_freshness,
        'current_file': current_data_file,
        'update_interval': config.REALTIME_UPDATE_INTERVAL,
        'frontend_interval': config.DATA_UPDATE_INTERVAL / 1000,  # 转换为秒
        'system_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.route('/api/update_position', methods=['POST'])
def update_position():
    """更新期权合约的持仓数量"""
    global user_position_overrides

    try:
        data = request.get_json()
        symbol = data.get('symbol')
        new_position = data.get('position')

        if not symbol:
            return jsonify({'success': False, 'message': '缺少合约代码'}), 400

        if new_position is None:
            return jsonify({'success': False, 'message': '缺少持仓数量'}), 400

        try:
            new_position = float(new_position)
        except (ValueError, TypeError):
            return jsonify({'success': False, 'message': '持仓数量必须是数字'}), 400

        # 保存用户修改
        user_position_overrides[symbol] = new_position

        print(f"用户修改持仓: {symbol} -> {new_position}")

        return jsonify({
            'success': True,
            'message': f'成功更新 {symbol} 的持仓数量为 {new_position}',
            'symbol': symbol,
            'position': new_position
        })

    except Exception as e:
        print(f"更新持仓数量时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

@app.route('/api/get_position_overrides')
def get_position_overrides():
    """获取用户修改的持仓数量"""
    global user_position_overrides
    return jsonify(user_position_overrides)

@app.route('/api/reset_positions', methods=['POST'])
def reset_positions():
    """重置所有持仓修改到原始值"""
    global user_position_overrides

    try:
        # 清空所有用户修改
        user_position_overrides.clear()

        print("用户重置了所有持仓修改")

        return jsonify({
            'success': True,
            'message': '已重置所有持仓修改到原始值'
        })

    except Exception as e:
        print(f"重置持仓时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'重置失败: {str(e)}'}), 500

@app.route('/api/reset_position/<symbol>', methods=['POST'])
def reset_single_position(symbol):
    """重置单个合约的持仓修改到原始值"""
    global user_position_overrides

    try:
        if symbol in user_position_overrides:
            del user_position_overrides[symbol]
            print(f"用户重置了 {symbol} 的持仓修改")
            message = f'已重置 {symbol} 的持仓到原始值'
        else:
            message = f'{symbol} 没有被修改过'

        return jsonify({
            'success': True,
            'message': message,
            'symbol': symbol
        })

    except Exception as e:
        print(f"重置单个持仓时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'重置失败: {str(e)}'}), 500

@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    """生成Excel报告和图片API"""
    try:
        # 获取前端发送的调整后数据
        adjusted_data = request.json.get('data', None)

        # 生成报告
        success, message = generate_excel_reports(adjusted_data)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'生成报告时出错: {str(e)}'})

def validate_uploaded_file(filepath):
    """验证上传的CSV文件"""
    try:
        # 读取文件进行验证
        try:
            df = pd.read_csv(filepath, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(filepath, encoding='gbk')

        # 检查基本结构
        if df.empty:
            return False, "文件为空"

        # 检查必需的列
        required_columns = ['代码', '数量', '方向', '交易所']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return False, f"缺少必需的列: {missing_columns}"

        # 检查期权数据
        option_count = len(df[df['代码'].str.len() > 6])
        if option_count == 0:
            return False, "文件中没有找到期权数据（代码长度应大于6）"

        # 检查数据质量
        valid_positions = df['数量'].notna().sum()
        if valid_positions == 0:
            return False, "没有有效的持仓数量数据"

        return True, f"文件验证通过，包含 {option_count} 个期权合约，{valid_positions} 个有效持仓"

    except Exception as e:
        return False, f"文件验证失败: {str(e)}"

@app.route('/api/upload_csv', methods=['POST'])
def upload_csv():
    """上传CSV文件API"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件被上传'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})

        if file and file.filename.endswith('.csv'):
            # 确保out目录存在
            out_dir = config.OUTPUT_DIR
            if not os.path.exists(out_dir):
                os.makedirs(out_dir)

            # 保存文件
            filename = f"uploaded_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            filepath = os.path.join(out_dir, filename)
            file.save(filepath)

            print(f"文件已保存到: {filepath}")

            # 验证文件
            is_valid, validation_message = validate_uploaded_file(filepath)
            if not is_valid:
                # 删除无效文件
                os.remove(filepath)
                return jsonify({'success': False, 'message': f'文件验证失败: {validation_message}'})

            print(f"文件验证成功: {validation_message}")

            # 使用新文件重新初始化系统
            if reinitialize_with_new_file(filepath):
                return jsonify({'success': True, 'message': f'文件上传成功，系统已切换到新数据: {filename}\n{validation_message}'})
            else:
                return jsonify({'success': False, 'message': '文件处理失败，请检查文件格式'})
        else:
            return jsonify({'success': False, 'message': '请上传CSV文件'})

    except Exception as e:
        print(f"上传文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'上传文件时出错: {str(e)}'})

def cleanup():
    """清理函数"""
    global system_initialized
    system_initialized = False
    shutdown_system()
    print("应用清理完成")

# 注册清理函数
atexit.register(cleanup)

if __name__ == '__main__':
    try:
        # 初始化应用
        if initialize_app():
            print("应用初始化成功，启动Web服务器...")
            # 启动 Flask 开发服务器
            app.run(debug=config.WEB_DEBUG, host=config.WEB_HOST, port=config.WEB_PORT, threaded=config.WEB_THREADED)
        else:
            print("应用初始化失败")
    except KeyboardInterrupt:
        print("\n用户中断程序")
    finally:
        cleanup()